package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
)

// intentClassifier 第一层意图分类器实现
type intentClassifier struct {
	deepseekService *service.DeepSeekService
	contextManager  *ContextManager
	logger          *logrus.Logger
	config          *ClassifierConfig
	entityExtractor *EntityExtractor
}

// NewIntentClassifier 创建意图分类器
func NewIntentClassifier(deepseekService *service.DeepSeekService, contextManager *ContextManager, logger *logrus.Logger) IntentClassifier {
	config := &ClassifierConfig{
		ConfidenceThreshold: 0.7,
		MaxRetries:          3,
		TimeoutSeconds:      30,
		EnableContextBoost:  true,
		ContextBoostFactor:  0.15,
	}

	return &intentClassifier{
		deepseekService: deepseekService,
		contextManager:  contextManager,
		logger:          logger,
		config:          config,
		entityExtractor: NewEntityExtractor(logger),
	}
}

// ClassifyIntent 分类意图
func (ic *intentClassifier) ClassifyIntent(ctx context.Context, message string, context *ConversationContext) (*ClassificationResult, error) {
	start := time.Now()

	ic.logger.WithFields(logrus.Fields{
		"message": message,
		"context": context != nil,
	}).Info("Starting intent classification")

	// 预处理消息
	normalizedMessage := ic.normalizeMessage(message)

	// 提取实体
	entities := ic.entityExtractor.ExtractEntities(normalizedMessage)

	// 构建分类提示
	systemPrompt := ic.buildClassificationPrompt()
	userPrompt := ic.buildUserPrompt(normalizedMessage, entities, context)

	// 调用DeepSeek API进行分类
	messages := []service.Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := ic.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to classify intent: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	// 解析分类结果
	result, err := ic.parseClassificationResponse(response.Choices[0].Message.Content, entities)
	if err != nil {
		ic.logger.WithError(err).Warn("Failed to parse classification response, using fallback")
		return ic.createFallbackResult(normalizedMessage, entities), nil
	}

	// 应用上下文增强
	if ic.config.EnableContextBoost && context != nil {
		ic.applyContextBoost(result, context)
	}

	ic.logger.WithFields(logrus.Fields{
		"intent":          result.Intent,
		"confidence":      result.Confidence,
		"entities_count":  len(result.Entities),
		"processing_time": time.Since(start),
	}).Info("Intent classification completed")

	return result, nil
}

// buildClassificationPrompt 构建分类系统提示
func (ic *intentClassifier) buildClassificationPrompt() string {
	return `你是一个专业的运维意图分类专家。请精确分析用户输入，识别其运维意图类型。

支持的意图类型及其特征：

1. connection_diagnosis - 连接诊断
   - 关键词：检查连接、测试连接、连接失败、登录问题、SSH问题
   - 示例：检查***********连接、测试主机登录、SSH连接报错

2. command_execution - 命令执行
   - 关键词：执行命令、运行、查看、显示
   - 示例：执行ls命令、查看进程、显示内存使用

3. system_monitoring - 系统监控
   - 关键词：监控、状态、使用率、性能、负载
   - 示例：查看CPU使用率、监控内存、系统负载

4. service_management - 服务管理
   - 关键词：服务、启动、停止、重启、状态
   - 示例：重启nginx、查看服务状态、停止apache

5. log_analysis - 日志分析
   - 关键词：日志、错误、查看日志、分析
   - 示例：查看错误日志、分析系统日志、检查应用日志

6. file_operations - 文件操作
   - 关键词：文件、目录、查看文件、编辑、传输
   - 示例：查看配置文件、编辑文件、上传文件

7. network_diagnostics - 网络诊断
   - 关键词：网络、ping、端口、连通性、网络测试
   - 示例：ping测试、检查端口、网络连通性

8. security_check - 安全检查
   - 关键词：安全、权限、扫描、检查权限
   - 示例：检查文件权限、安全扫描、权限审计

9. host_management - 主机管理
   - 关键词：主机、添加主机、删除主机、主机列表
   - 示例：添加新主机、删除主机、查看主机列表

10. performance_analysis - 性能分析
    - 关键词：性能、瓶颈、分析、优化
    - 示例：性能分析、查找瓶颈、系统优化

11. general_chat - 通用对话
    - 关键词：你好、帮助、介绍、感谢
    - 示例：你好、请帮助我、介绍功能

【重要】特殊场景识别规则：
- "检查[IP]执行命令报什么错误" → connection_diagnosis
- "检查[IP]登录报什么错误" → connection_diagnosis  
- "测试[IP]连接" → connection_diagnosis
- "[IP] [用户名] [密码]" → host_management (添加主机)

请返回JSON格式：
{
  "intent": "意图类型",
  "confidence": 0.95,
  "entities": {
    "ip_address": "***********",
    "action": "check_connection"
  },
  "reasoning": "识别理由"
}`
}

// buildUserPrompt 构建用户提示
func (ic *intentClassifier) buildUserPrompt(message string, entities map[string]interface{}, context *ConversationContext) string {
	prompt := fmt.Sprintf("用户输入：%s", message)

	if len(entities) > 0 {
		entitiesJSON, _ := json.Marshal(entities)
		prompt += fmt.Sprintf("\n已识别实体：%s", string(entitiesJSON))
	}

	if context != nil && len(context.RecentIntents) > 0 {
		prompt += fmt.Sprintf("\n最近意图：%v", context.RecentIntents)
	}

	return prompt
}

// parseClassificationResponse 解析分类响应
func (ic *intentClassifier) parseClassificationResponse(content string, entities map[string]interface{}) (*ClassificationResult, error) {
	// 尝试解析JSON
	var result struct {
		Intent     string                 `json:"intent"`
		Confidence float64                `json:"confidence"`
		Entities   map[string]interface{} `json:"entities"`
		Reasoning  string                 `json:"reasoning"`
	}

	if err := json.Unmarshal([]byte(content), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 验证意图类型
	if !ic.isValidIntent(result.Intent) {
		return nil, fmt.Errorf("invalid intent type: %s", result.Intent)
	}

	// 合并实体
	mergedEntities := make(map[string]interface{})
	for k, v := range entities {
		mergedEntities[k] = v
	}
	for k, v := range result.Entities {
		mergedEntities[k] = v
	}

	return &ClassificationResult{
		Intent:     result.Intent,
		Confidence: result.Confidence,
		Entities:   mergedEntities,
		Context:    make(map[string]interface{}),
		Reasoning:  result.Reasoning,
		Timestamp:  time.Now(),
	}, nil
}

// createFallbackResult 创建降级结果
func (ic *intentClassifier) createFallbackResult(message string, entities map[string]interface{}) *ClassificationResult {
	// 基于规则的简单分类
	intent := ic.classifyByRules(message, entities)

	return &ClassificationResult{
		Intent:     intent,
		Confidence: 0.6, // 降级结果置信度较低
		Entities:   entities,
		Context:    make(map[string]interface{}),
		Reasoning:  "Fallback classification based on rules",
		Timestamp:  time.Now(),
	}
}

// classifyByRules 基于规则的分类
func (ic *intentClassifier) classifyByRules(message string, entities map[string]interface{}) string {
	message = strings.ToLower(message)

	// 连接诊断相关
	if strings.Contains(message, "检查") && (strings.Contains(message, "连接") || strings.Contains(message, "登录") || strings.Contains(message, "错误")) {
		return IntentConnectionDiagnosis
	}

	// 主机管理相关
	if strings.Contains(message, "添加主机") || strings.Contains(message, "主机添加") {
		return IntentHostManagement
	}

	// 命令执行相关
	if strings.Contains(message, "执行") || strings.Contains(message, "运行") {
		return IntentCommandExecution
	}

	// 系统监控相关
	if strings.Contains(message, "监控") || strings.Contains(message, "状态") || strings.Contains(message, "使用率") {
		return IntentSystemMonitoring
	}

	// 默认为通用对话
	return IntentGeneralChat
}

// applyContextBoost 应用上下文增强
func (ic *intentClassifier) applyContextBoost(result *ClassificationResult, context *ConversationContext) {
	if len(context.RecentIntents) == 0 {
		return
	}

	// 如果最近的意图与当前意图相同，增加置信度
	recentIntent := context.RecentIntents[len(context.RecentIntents)-1]
	if recentIntent == result.Intent {
		result.Confidence = min(1.0, result.Confidence+ic.config.ContextBoostFactor)
		result.Context["context_boost"] = true
	}
}

// normalizeMessage 标准化消息
func (ic *intentClassifier) normalizeMessage(message string) string {
	// 去除多余空格
	message = regexp.MustCompile(`\s+`).ReplaceAllString(strings.TrimSpace(message), " ")
	return message
}

// isValidIntent 验证意图类型
func (ic *intentClassifier) isValidIntent(intent string) bool {
	validIntents := ic.GetSupportedIntents()
	for _, validIntent := range validIntents {
		if intent == validIntent {
			return true
		}
	}
	return false
}

// GetSupportedIntents 获取支持的意图类型
func (ic *intentClassifier) GetSupportedIntents() []string {
	return []string{
		IntentConnectionDiagnosis,
		IntentCommandExecution,
		IntentSystemMonitoring,
		IntentServiceManagement,
		IntentLogAnalysis,
		IntentFileOperations,
		IntentNetworkDiagnosis,
		IntentSecurityCheck,
		IntentHostManagement,
		IntentPerformanceAnalysis,
		IntentGeneralChat,
	}
}

// UpdateContext 更新上下文
func (ic *intentClassifier) UpdateContext(sessionID string, result *ClassificationResult) error {
	if ic.contextManager == nil {
		return nil
	}

	context := ic.contextManager.GetOrCreateContext(sessionID, 1) // 暂时使用固定用户ID
	context.AddIntent(result.Intent)

	return nil
}

// min 辅助函数
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
