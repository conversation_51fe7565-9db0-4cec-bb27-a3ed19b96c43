package main

import (
	"fmt"
	"log"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/logger"
	"aiops-platform/internal/model"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	_ = logger.New(cfg.Log)

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("=== 检查用户表 ===")

	// 查询所有用户
	var users []model.User
	if err := db.Find(&users).Error; err != nil {
		log.Fatalf("Failed to query users: %v", err)
	}

	fmt.Printf("用户总数: %d\n", len(users))
	for _, user := range users {
		fmt.Printf("ID: %d, Username: %s, Email: %s, Role: %s\n",
			user.ID, user.Username, user.Email, user.Role)
	}

	// 如果没有用户，创建一个默认用户
	if len(users) == 0 {
		fmt.Println("\n没有用户记录，创建默认用户...")

		defaultUser := &model.User{
			Username:     "admin",
			Email:        "<EMAIL>",
			FullName:     "系统管理员",
			Role:         "admin",
			IsActive:     true,
			PasswordHash: "$2a$10$dummy.hash.for.testing.purposes.only", // 临时密码哈希
		}

		if err := db.Create(defaultUser).Error; err != nil {
			log.Fatalf("Failed to create default user: %v", err)
		}

		fmt.Printf("默认用户创建成功，ID: %d\n", defaultUser.ID)
	}

	fmt.Println("\n=== 检查完成 ===")
}
