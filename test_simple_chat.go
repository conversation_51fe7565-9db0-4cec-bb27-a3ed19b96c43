package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	baseURL := "http://localhost:8080"

	fmt.Println("🧪 简单聊天测试")

	// 创建会话
	sessionID := createSession(baseURL, "智能对话测试")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}
	fmt.Printf("✅ 会话创建成功: %s\n", sessionID)

	// 等待一下
	time.Sleep(1 * time.Second)

	// 发送测试消息
	message := "添加主机 ************** root 1qaz#EDC"
	fmt.Printf("\n📤 发送消息: %s\n", message)

	response := sendMessage(baseURL, sessionID, message)
	fmt.Printf("📥 AI回复: %s\n", response.Data.AIResponse)
	fmt.Printf("🎯 意图: %s\n", response.Data.Intent)
}

type SessionResponse struct {
	Code int `json:"code"`
	Data struct {
		SessionID string `json:"session_id"`
	} `json:"data"`
}

type MessageResponse struct {
	Code int `json:"code"`
	Data struct {
		AIResponse string `json:"ai_response"`
		Intent     string `json:"intent"`
	} `json:"data"`
}

func createSession(baseURL, title string) string {
	url := baseURL + "/api/v1/chat/sessions"

	reqBody := map[string]string{"title": title}
	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建会话失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var sessionResp SessionResponse
	if err := json.Unmarshal(body, &sessionResp); err != nil {
		fmt.Printf("解析会话响应失败: %v\n", err)
		return ""
	}

	return sessionResp.Data.SessionID
}

func sendMessage(baseURL, sessionID, content string) *MessageResponse {
	url := baseURL + "/api/v1/chat/message"

	reqBody := map[string]interface{}{
		"session_id": sessionID,
		"content":    content,
		"stream":     false,
	}

	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("发送消息失败: %v\n", err)
		return &MessageResponse{}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var msgResp MessageResponse
	if err := json.Unmarshal(body, &msgResp); err != nil {
		fmt.Printf("解析消息响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return &MessageResponse{}
	}

	return &msgResp
}
