# AI对话运维管理平台 - 配置文件

# 应用配置
app:
  name: "aiops-platform"
  env: "development"
  debug: true
  port: 8080
  version: "1.0.0"

# 数据库配置
database:
  path: "./data/aiops.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "10m"

# JWT配置
jwt:
  secret: "dev-jwt-secret-key-for-local-development-only"
  access_token_ttl: "15m"
  refresh_token_ttl: "168h"
  issuer: "aiops-platform"
  max_concurrent_sessions: 5

# DeepSeek API配置
deepseek:
  api_key: "***********************************"  # 请通过环境变量 AIOPS_DEEPSEEK_API_KEY 设置真实的API密钥
  api_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  timeout: "30s"
  max_retries: 3
  max_context_tokens: 4000
  temperature: 0.7
  top_p: 0.9

# 安全配置
security:
  encryption_key: "dev-encryption-key-32bytes-long!"
  password_hash_cost: 12
  session_timeout: "24h"
  rate_limit:
    enabled: true
    global: "1000/min"
    per_user: "100/min"
    per_ip: "200/min"

# Redis配置
redis:
  enabled: false
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10 

# SSH配置
ssh:
  timeout: "30s"
  max_connections: 10
  idle_timeout: "5m"
  health_check_interval: "1m"

# 日志配置
log:
  level: "info"
  file: "./logs/aiops.log"
  max_size: 100
  retention_days: 30
  format: "json"

# 监控配置
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

# 缓存配置 
cache:
  enabled: true
  l1_size: "100MB"
  l1_ttl: "5m"
  l2_ttl: "1h"

# Agent平台配置
agent:
  enabled: true   # 启用Agent平台进行测试
  max_concurrent_requests: 10
  health_check_interval: 30
  enable_auto_registration: true
