package service

import (
	"context"
	"time"

	"aiops-platform/internal/auth"
	"aiops-platform/internal/config"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Services 服务集合
type Services struct {
	DB    *gorm.DB
	User  UserService
	Host  HostService
	Alert AlertService
	Chat  ChatService
	AI    AIService // 新增AI服务
	// Agent            AgentService      // Agent服务暂时移除
	Workflow         WorkflowService   // 新增工作流服务
	ActionExecutor   *ActionExecutor   // 操作执行器
	ActionRecognizer *ActionRecognizer // 操作识别器
	Monitoring       *MonitoringEngine // 监控引擎
	WebSocket        *WebSocketManager // WebSocket管理器
	Auth             AuthService
	Config           ConfigService
	Operation        OperationService
	Stats            StatsService
	EnhancedJWT      *auth.EnhancedJWTManager
}

// NewServices 创建服务集合
func NewServices(cfg *config.Config, db *gorm.DB, logger *logrus.Logger) (*Services, error) {
	// 创建密码管理器
	passwordManager := auth.NewPasswordManager(cfg.Security.PasswordHashCost)

	// 创建增强的JWT管理器
	enhancedJWT := auth.NewEnhancedJWTManager(cfg.JWT, db, logger)

	// 创建服务实例
	userService := NewUserService(db, passwordManager, logger)
	hostService := NewHostService(db, cfg, logger)
	alertService := NewAlertService(db, logger)
	chatService := NewChatService(db, cfg, logger)

	// 创建AI服务
	aiService := NewAIService(db, cfg, logger, hostService)

	// agentService := NewAgentService(db, cfg, logger)                               // Agent服务暂时移除
	workflowService := NewWorkflowService(db, cfg, logger, aiService, hostService) // 创建工作流服务

	// 创建操作相关服务
	actionRecognizer := NewActionRecognizer(logger, hostService)
	toolManager := NewToolManager(logger, hostService)
	actionExecutor := NewActionExecutor(db, logger, hostService, toolManager)

	// 创建WebSocket管理器
	wsManager := NewWebSocketManager(logger)

	// 设置WebSocket管理器的聊天服务和AI服务
	wsManager.SetChatService(chatService)
	wsManager.SetAIService(aiService)

	// 创建监控引擎
	monitoringEngine := NewMonitoringEngine(db, logger, hostService, wsManager, alertService)

	// 启动监控引擎
	go func() {
		if err := monitoringEngine.Start(context.Background()); err != nil {
			logger.WithError(err).Error("Failed to start monitoring engine")
		}
	}()

	// TODO: 实现其他服务
	// statsService := NewStatsService(db, logger)
	// authService := NewAuthService(db, jwtManager, passwordManager, logger)
	// configService := NewConfigService(db, logger)
	// operationService := NewOperationService(db, logger)

	return &Services{
		DB:    db,
		User:  userService,
		Host:  hostService,
		Alert: alertService,
		Chat:  chatService,
		AI:    aiService, // 添加AI服务
		// Agent:            agentService,     // Agent服务暂时移除
		Workflow:         workflowService,  // 添加工作流服务
		ActionExecutor:   actionExecutor,   // 添加操作执行器
		ActionRecognizer: actionRecognizer, // 添加操作识别器
		Monitoring:       monitoringEngine, // 添加监控引擎
		WebSocket:        wsManager,        // 添加WebSocket管理器
		Stats:            nil,
		Auth:             nil,
		Config:           nil,
		Operation:        nil,
		EnhancedJWT:      enhancedJWT,
	}, nil
}

// Close 关闭服务
func (s *Services) Close() {
	// 这里可以添加清理逻辑
	// 例如关闭连接池、清理缓存等
}

// UserService 用户服务接口
type UserService interface {
	CreateUser(req *model.UserCreateRequest) (*model.UserResponse, error)
	GetUserByID(id int64) (*model.UserResponse, error)
	GetUserByUsername(username string) (*model.UserResponse, error)
	GetUserByEmail(email string) (*model.UserResponse, error)
	UpdateUser(id int64, req *model.UserUpdateRequest) (*model.UserResponse, error)
	DeleteUser(id int64) error
	ListUsers(req *model.UserListQuery) (*model.UserListResponse, error)
	ChangePassword(id int64, req *model.PasswordChangeRequest) error
	ResetPassword(id int64, newPassword string) error
	LockUser(id int64, duration time.Duration) error
	UnlockUser(id int64) error
}

// HostService 主机服务接口
type HostService interface {
	CreateHost(req *model.HostCreateRequest) (*model.HostResponse, error)
	GetHostByID(id int64) (*model.HostResponse, error)
	GetHostByName(name string) (*model.HostResponse, error)
	UpdateHost(id int64, req *model.HostUpdateRequest) (*model.HostResponse, error)
	DeleteHost(id int64) error
	ListHosts(req *model.HostListQuery) (*model.HostListResponse, error)
	TestConnection(id int64) (*model.HostTestResponse, error)
	ExecuteCommand(id int64, req *model.CommandExecuteRequest) (*model.CommandExecuteResponse, error)
	GetHostStatus(id int64) (*model.HostResponse, error)
	UpdateHostStatus(id int64, status string) error
}

// AlertService 告警服务接口
type AlertService interface {
	CreateAlert(req *model.AlertCreateRequest) (*model.AlertResponse, error)
	GetAlertByID(id int64) (*model.AlertResponse, error)
	UpdateAlert(id int64, req *model.AlertUpdateRequest) (*model.AlertResponse, error)
	DeleteAlert(id int64) error
	ListAlerts(req *model.AlertListQuery) (*model.AlertListResponse, error)
	AcknowledgeAlert(id int64, userID int64, comment string) error
	ResolveAlert(id int64, resolution string) error
	CloseAlert(id int64) error
	GetAlertSummary() (*model.AlertSummary, error)
}

// ChatService 对话服务接口
type ChatService interface {
	CreateSession(userID int64, req *model.ChatSessionCreateRequest) (*model.ChatSessionResponse, error)
	CreateSessionWithID(session *model.ChatSession) error
	GetSessionByID(id int64) (*model.ChatSessionResponse, error)
	GetSessionBySessionID(sessionID string) (*model.ChatSessionResponse, error)
	ListSessions(userID int64, req *model.ChatSessionListQuery) (*model.ChatSessionListResponse, error)
	SendMessage(req *model.ChatMessageRequest) (*model.ChatMessageResponse, error)
	GetMessages(sessionID string, req *model.ChatSessionListQuery) (*model.ChatSessionListResponse, error)
	EndSession(sessionID string) error
	DeleteSession(sessionID string) error
}

// TODO: 暂时注释未实现的服务接口
/*
// AuthService 认证服务接口
type AuthService interface {
	Login(req *LoginRequest) (*LoginResponse, error)
	Logout(userID int64, sessionID string) error
	RefreshToken(refreshToken string) (*TokenResponse, error)
	ValidateToken(token string) (*UserContext, error)
	RevokeToken(token string) error
	GetUserPermissions(userID int64) ([]string, error)
	HasPermission(userID int64, resource, action string) bool
}

// ConfigService 配置服务接口
type ConfigService interface {
	GetConfig(key string) (*ConfigResponse, error)
	GetConfigs(category string) ([]*ConfigResponse, error)
	UpdateConfig(key string, req *UpdateConfigRequest) (*ConfigResponse, error)
	GetAllConfigs() ([]*ConfigResponse, error)
}

// OperationService 操作日志服务接口
type OperationService interface {
	LogOperation(req *LogOperationRequest) error
	GetOperationByID(id int64) (*OperationResponse, error)
	ListOperations(req *ListOperationsRequest) (*ListOperationsResponse, error)
	GetOperationStats(req *OperationStatsRequest) (*OperationStatsResponse, error)
}
*/

// 临时接口定义
type AuthService interface{}
type ConfigService interface{}
type OperationService interface{}

// StatsService 统计服务接口 (暂时简化)
type StatsService interface {
	// TODO: 实现统计功能
}

// 请求和响应结构体定义
type CreateUserRequest struct {
	Username   string `json:"username" binding:"required"`
	Password   string `json:"password" binding:"required"`
	Email      string `json:"email" binding:"required,email"`
	FullName   string `json:"full_name" binding:"required"`
	Role       string `json:"role" binding:"required"`
	Phone      string `json:"phone"`
	Department string `json:"department"`
}

type UpdateUserRequest struct {
	Email      string `json:"email" binding:"omitempty,email"`
	FullName   string `json:"full_name"`
	Role       string `json:"role"`
	IsActive   *bool  `json:"is_active"`
	Phone      string `json:"phone"`
	Department string `json:"department"`
}

type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

type ListUsersRequest struct {
	Page       int    `json:"page"`
	Limit      int    `json:"limit"`
	Role       string `json:"role"`
	IsActive   *bool  `json:"is_active"`
	Search     string `json:"search"`
	Department string `json:"department"`
}

type UserResponse struct {
	ID         int64  `json:"id"`
	Username   string `json:"username"`
	Email      string `json:"email"`
	FullName   string `json:"full_name"`
	Role       string `json:"role"`
	IsActive   bool   `json:"is_active"`
	Phone      string `json:"phone"`
	Department string `json:"department"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
}

type ListUsersResponse struct {
	Users      []*UserResponse `json:"users"`
	Pagination *Pagination     `json:"pagination"`
}

type Pagination struct {
	Page    int   `json:"page"`
	Limit   int   `json:"limit"`
	Total   int64 `json:"total"`
	Pages   int   `json:"pages"`
	HasNext bool  `json:"has_next"`
	HasPrev bool  `json:"has_prev"`
}

// 其他服务的请求响应结构体将在各自的服务文件中定义
