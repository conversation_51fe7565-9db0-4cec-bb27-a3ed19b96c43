# AI对话运维管理平台 - 环境配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# =============================================================================
# 基础配置
# =============================================================================
ENV=development
DEBUG=true
LOG_LEVEL=info
PORT=8080

# =============================================================================
# 数据库配置
# =============================================================================
# SQLite数据库文件路径
DB_PATH=./data/aiops.db
# 数据库连接池配置
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=1h
DB_CONN_MAX_IDLE_TIME=10m

# =============================================================================
# DeepSeek API配置
# =============================================================================
# DeepSeek API密钥 (必填)
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
# DeepSeek API地址
DEEPSEEK_API_URL=https://api.deepseek.com
# 使用的模型名称
DEEPSEEK_MODEL=deepseek-chat
# API调用超时时间
DEEPSEEK_TIMEOUT=30s
# 最大重试次数
DEEPSEEK_MAX_RETRIES=3
# 最大上下文Token数
DEEPSEEK_MAX_CONTEXT_TOKENS=4000

# =============================================================================
# JWT认证配置
# =============================================================================
# JWT签名密钥 (生产环境请使用强密钥)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
# Access Token有效期
JWT_ACCESS_TOKEN_TTL=15m
# Refresh Token有效期
JWT_REFRESH_TOKEN_TTL=7d
# JWT发行者
JWT_ISSUER=aiops-platform

# =============================================================================
# 安全配置
# =============================================================================
# 数据加密密钥 (32字节，生产环境请使用随机生成的密钥)
ENCRYPTION_KEY=your-32-byte-encryption-key-here!!
# 密码哈希成本 (bcrypt cost)
PASSWORD_HASH_COST=12
# 会话超时时间
SESSION_TIMEOUT=24h
# 最大并发会话数
MAX_CONCURRENT_SESSIONS=5

# =============================================================================
# Redis配置 (可选，用于缓存和会话存储)
# =============================================================================
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10

# =============================================================================
# SSH连接配置
# =============================================================================
# SSH连接超时时间
SSH_TIMEOUT=30s
# 最大SSH连接数
SSH_MAX_CONNECTIONS=10
# SSH连接空闲超时
SSH_IDLE_TIMEOUT=5m
# SSH健康检查间隔
SSH_HEALTH_CHECK_INTERVAL=1m

# =============================================================================
# 监控和日志配置
# =============================================================================
# 日志文件路径
LOG_FILE=./logs/aiops.log
# 日志轮转大小 (MB)
LOG_MAX_SIZE=100
# 日志保留天数
LOG_RETENTION_DAYS=30
# 是否启用Prometheus指标
METRICS_ENABLED=true
# Prometheus指标端口
METRICS_PORT=9090

# =============================================================================
# 告警配置
# =============================================================================
# 告警保留天数
ALERT_RETENTION_DAYS=90
# 邮件通知配置
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=smtp.example.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# 钉钉通知配置
DINGTALK_ENABLED=false
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your-token

# =============================================================================
# 性能配置
# =============================================================================
# API限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_GLOBAL=1000/min
RATE_LIMIT_PER_USER=100/min
RATE_LIMIT_PER_IP=200/min

# 缓存配置
CACHE_ENABLED=true
CACHE_L1_SIZE=100MB
CACHE_L1_TTL=5m
CACHE_L2_TTL=1h

# 工作池配置
WORKER_POOL_SIZE=10
WORKER_QUEUE_SIZE=1000

# =============================================================================
# 开发配置
# =============================================================================
# 是否启用热重载
HOT_RELOAD=true
# 是否启用性能分析
PROFILING_ENABLED=false
# 是否启用调试模式
DEBUG_MODE=true
# 跨域配置
CORS_ENABLED=true
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# =============================================================================
# 部署配置
# =============================================================================
# 健康检查路径
HEALTH_CHECK_PATH=/health
# 优雅关闭超时时间
GRACEFUL_SHUTDOWN_TIMEOUT=30s
# 静态文件目录
STATIC_DIR=./web/static
# 模板文件目录
TEMPLATE_DIR=./web/templates

# =============================================================================
# 备份配置
# =============================================================================
# 是否启用自动备份
BACKUP_ENABLED=true
# 备份间隔
BACKUP_INTERVAL=6h
# 备份保留天数
BACKUP_RETENTION_DAYS=30
# 备份存储路径
BACKUP_STORAGE_PATH=./backups
# 是否压缩备份
BACKUP_COMPRESSION=true
# 是否加密备份
BACKUP_ENCRYPTION=true

# =============================================================================
# 高级配置
# =============================================================================
# 是否启用分布式锁
DISTRIBUTED_LOCK_ENABLED=false
# 分布式锁Redis配置
LOCK_REDIS_HOST=localhost
LOCK_REDIS_PORT=6379

# 是否启用链路追踪
TRACING_ENABLED=false
# Jaeger配置
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# 是否启用服务发现
SERVICE_DISCOVERY_ENABLED=false
# Consul配置
CONSUL_HOST=localhost
CONSUL_PORT=8500
