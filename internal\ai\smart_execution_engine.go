package ai

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"
	"github.com/sirupsen/logrus"
)

// SmartExecutionEngine 智能执行引擎
type SmartExecutionEngine struct {
	deepseekService           *service.DeepSeekService
	hostService               workflow.HostServiceInterface
	hostStatusHandler         *hostStatusDiagnosisHandler
	sshConnectionTestHandler  *sshConnectionTestHandler
	logger                    *logrus.Logger
	config                    *SmartExecutionConfig
}

// SmartExecutionConfig 智能执行配置
type SmartExecutionConfig struct {
	AutoExecuteEnabled    bool          `json:"auto_execute_enabled"`
	MaxExecutionTime      time.Duration `json:"max_execution_time"`
	EnableDetailedLogging bool          `json:"enable_detailed_logging"`
	RetryAttempts         int           `json:"retry_attempts"`
}

// NewSmartExecutionEngine 创建智能执行引擎
func NewSmartExecutionEngine(
	deepseekService *service.DeepSeekService,
	hostService workflow.HostServiceInterface,
	logger *logrus.Logger,
) *SmartExecutionEngine {
	config := &SmartExecutionConfig{
		AutoExecuteEnabled:    true,
		MaxExecutionTime:      60 * time.Second,
		EnableDetailedLogging: true,
		RetryAttempts:         2,
	}

	return &SmartExecutionEngine{
		deepseekService:          deepseekService,
		hostService:              hostService,
		hostStatusHandler:        NewHostStatusDiagnosisHandler(deepseekService, hostService, logger).(*hostStatusDiagnosisHandler),
		sshConnectionTestHandler: NewSSHConnectionTestHandler(deepseekService, hostService, logger).(*sshConnectionTestHandler),
		logger:                   logger,
		config:                   config,
	}
}

// ExecuteIntent 智能执行意图
func (see *SmartExecutionEngine) ExecuteIntent(ctx context.Context, intent *service.IntentResult, originalMessage string) (*SmartExecutionResult, error) {
	if !see.config.AutoExecuteEnabled {
		return &SmartExecutionResult{
			Intent:      intent.Type,
			Executed:    false,
			Message:     "自动执行已禁用，需要手动确认",
			Suggestions: []string{"启用自动执行功能", "手动执行相关操作"},
		}, nil
	}

	see.logger.WithFields(logrus.Fields{
		"intent":      intent.Type,
		"confidence":  intent.Confidence,
		"parameters":  intent.Parameters,
		"message":     originalMessage,
	}).Info("开始智能执行意图")

	switch intent.Type {
	case IntentHostStatusDiagnosis:
		return see.executeHostStatusDiagnosis(ctx, intent, originalMessage)
	case IntentSSHConnectionTest:
		return see.executeSSHConnectionTest(ctx, intent, originalMessage)
	case IntentConnectionDiagnosis:
		return see.executeConnectionDiagnosis(ctx, intent, originalMessage)
	default:
		return &SmartExecutionResult{
			Intent:      intent.Type,
			Executed:    false,
			Message:     fmt.Sprintf("意图类型 '%s' 暂不支持自动执行", intent.Type),
			Suggestions: []string{"手动执行相关操作", "查看帮助文档"},
		}, nil
	}
}

// executeHostStatusDiagnosis 执行主机状态诊断
func (see *SmartExecutionEngine) executeHostStatusDiagnosis(ctx context.Context, intent *service.IntentResult, originalMessage string) (*SmartExecutionResult, error) {
	see.logger.Info("执行主机状态诊断")

	// 提取目标主机IP
	targetHost := see.extractHostFromIntent(intent)
	if targetHost == "" {
		return &SmartExecutionResult{
			Intent:   intent.Type,
			Executed: false,
			Message:  "无法从输入中提取主机IP地址",
			Suggestions: []string{
				"请提供明确的IP地址，如：查看***********为啥是离线状态",
				"确认IP地址格式是否正确",
			},
		}, nil
	}

	// 执行主机状态诊断
	diagnosisResult, err := see.hostStatusHandler.ExecuteWithHostService(ctx, targetHost)
	if err != nil {
		see.logger.WithError(err).Error("主机状态诊断执行失败")
		return &SmartExecutionResult{
			Intent:   intent.Type,
			Executed: false,
			Message:  fmt.Sprintf("主机状态诊断失败: %v", err),
			Suggestions: []string{
				"检查网络连接",
				"确认主机IP地址是否正确",
				"稍后重试",
			},
		}, nil
	}

	// 构建执行结果
	message := see.buildHostStatusMessage(diagnosisResult)
	suggestions := see.buildHostStatusSuggestions(diagnosisResult)

	return &SmartExecutionResult{
		Intent:      intent.Type,
		Executed:    true,
		Message:     message,
		Details:     diagnosisResult.Details,
		Suggestions: suggestions,
		Data: map[string]interface{}{
			"host_ip":   diagnosisResult.HostIP,
			"status":    diagnosisResult.Status,
			"timestamp": diagnosisResult.Timestamp,
		},
	}, nil
}

// executeSSHConnectionTest 执行SSH连接测试
func (see *SmartExecutionEngine) executeSSHConnectionTest(ctx context.Context, intent *service.IntentResult, originalMessage string) (*SmartExecutionResult, error) {
	see.logger.Info("执行SSH连接测试")

	// 提取目标主机IP
	targetHost := see.extractHostFromIntent(intent)
	if targetHost == "" {
		return &SmartExecutionResult{
			Intent:   intent.Type,
			Executed: false,
			Message:  "无法从输入中提取主机IP地址",
			Suggestions: []string{
				"请提供明确的IP地址，如：ssh连接下***********看看报什么错误",
				"确认IP地址格式是否正确",
			},
		}, nil
	}

	// 执行SSH连接测试
	testResult, err := see.sshConnectionTestHandler.ExecuteWithHostService(ctx, targetHost)
	if err != nil {
		see.logger.WithError(err).Error("SSH连接测试执行失败")
		return &SmartExecutionResult{
			Intent:   intent.Type,
			Executed: false,
			Message:  fmt.Sprintf("SSH连接测试失败: %v", err),
			Suggestions: []string{
				"检查网络连接",
				"确认SSH服务是否运行",
				"稍后重试",
			},
		}, nil
	}

	// 构建执行结果
	message := see.buildSSHTestMessage(testResult)
	suggestions := testResult.Suggestions

	return &SmartExecutionResult{
		Intent:      intent.Type,
		Executed:    true,
		Message:     message,
		Details:     testResult.Details,
		Suggestions: suggestions,
		Data: map[string]interface{}{
			"host_ip":       testResult.HostIP,
			"success":       testResult.Success,
			"error_message": testResult.ErrorMessage,
			"timestamp":     testResult.Timestamp,
		},
	}, nil
}

// executeConnectionDiagnosis 执行连接诊断
func (see *SmartExecutionEngine) executeConnectionDiagnosis(ctx context.Context, intent *service.IntentResult, originalMessage string) (*SmartExecutionResult, error) {
	see.logger.Info("执行连接诊断")

	// 对于连接诊断，我们可以结合主机状态诊断和SSH连接测试
	targetHost := see.extractHostFromIntent(intent)
	if targetHost == "" {
		return &SmartExecutionResult{
			Intent:   intent.Type,
			Executed: false,
			Message:  "无法从输入中提取主机信息",
			Suggestions: []string{
				"请提供明确的主机信息",
				"确认输入格式是否正确",
			},
		}, nil
	}

	// 先执行主机状态诊断
	diagnosisResult, _ := see.hostStatusHandler.ExecuteWithHostService(ctx, targetHost)
	
	// 再执行SSH连接测试
	sshResult, _ := see.sshConnectionTestHandler.ExecuteWithHostService(ctx, targetHost)

	// 综合分析结果
	message := see.buildComprehensiveDiagnosisMessage(diagnosisResult, sshResult, targetHost)
	suggestions := see.buildComprehensiveSuggestions(diagnosisResult, sshResult)

	return &SmartExecutionResult{
		Intent:      intent.Type,
		Executed:    true,
		Message:     message,
		Suggestions: suggestions,
		Data: map[string]interface{}{
			"host_ip":          targetHost,
			"host_diagnosis":   diagnosisResult,
			"ssh_test_result":  sshResult,
			"timestamp":        time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// extractHostFromIntent 从意图中提取主机信息
func (see *SmartExecutionEngine) extractHostFromIntent(intent *service.IntentResult) string {
	if ip, exists := intent.Parameters["ip_address"]; exists {
		if ipStr, ok := ip.(string); ok {
			return ipStr
		}
	}

	if hostname, exists := intent.Parameters["hostname"]; exists {
		if hostnameStr, ok := hostname.(string); ok {
			return hostnameStr
		}
	}

	return ""
}

// buildHostStatusMessage 构建主机状态消息
func (see *SmartExecutionEngine) buildHostStatusMessage(result *HostDiagnosisResult) string {
	if result.Status == "online" {
		return fmt.Sprintf("✅ 主机 %s 状态正常，当前在线", result.HostIP)
	} else if result.Status == "offline" {
		return fmt.Sprintf("❌ 主机 %s 当前离线：%s", result.HostIP, result.Message)
	} else {
		return fmt.Sprintf("⚠️ 主机 %s 状态未知：%s", result.HostIP, result.Message)
	}
}

// buildHostStatusSuggestions 构建主机状态建议
func (see *SmartExecutionEngine) buildHostStatusSuggestions(result *HostDiagnosisResult) []string {
	if result.Status == "online" {
		return []string{"主机运行正常", "可以进行其他操作"}
	} else if result.Status == "offline" {
		return []string{
			"检查主机是否开机",
			"验证网络连接",
			"确认SSH服务状态",
			"检查防火墙设置",
		}
	} else {
		return []string{
			"先添加主机到系统中",
			"确认IP地址是否正确",
			"检查网络连通性",
		}
	}
}

// buildSSHTestMessage 构建SSH测试消息
func (see *SmartExecutionEngine) buildSSHTestMessage(result *SSHTestResult) string {
	if result.Success {
		return fmt.Sprintf("✅ SSH连接到 %s 成功", result.HostIP)
	} else {
		return fmt.Sprintf("❌ SSH连接到 %s 失败：%s", result.HostIP, result.ErrorMessage)
	}
}

// buildComprehensiveDiagnosisMessage 构建综合诊断消息
func (see *SmartExecutionEngine) buildComprehensiveDiagnosisMessage(hostResult *HostDiagnosisResult, sshResult *SSHTestResult, targetHost string) string {
	message := fmt.Sprintf("🔍 主机 %s 连接诊断结果：\n", targetHost)
	
	if hostResult != nil {
		message += fmt.Sprintf("• 主机状态：%s\n", hostResult.Message)
	}
	
	if sshResult != nil {
		if sshResult.Success {
			message += "• SSH连接：正常\n"
		} else {
			message += fmt.Sprintf("• SSH连接：失败 - %s\n", sshResult.ErrorMessage)
		}
	}

	return message
}

// buildComprehensiveSuggestions 构建综合建议
func (see *SmartExecutionEngine) buildComprehensiveSuggestions(hostResult *HostDiagnosisResult, sshResult *SSHTestResult) []string {
	suggestions := []string{}
	
	if hostResult != nil && hostResult.Status == "offline" {
		suggestions = append(suggestions, "主机离线，检查主机电源和网络")
	}
	
	if sshResult != nil && !sshResult.Success {
		suggestions = append(suggestions, sshResult.Suggestions...)
	}
	
	if len(suggestions) == 0 {
		suggestions = append(suggestions, "系统运行正常")
	}

	return suggestions
}

// SmartExecutionResult 智能执行结果
type SmartExecutionResult struct {
	Intent      string                 `json:"intent"`
	Executed    bool                   `json:"executed"`
	Message     string                 `json:"message"`
	Details     []string               `json:"details,omitempty"`
	Suggestions []string               `json:"suggestions"`
	Data        map[string]interface{} `json:"data,omitempty"`
}
