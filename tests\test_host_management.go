package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/logger"
	"aiops-platform/internal/service"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	loggerInstance := logger.New(cfg.Log)

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 创建主机服务
	hostService := service.NewHostService(db, cfg, loggerInstance)

	// 创建工具管理器
	toolManager := service.NewToolManagerWithDB(loggerInstance, hostService, db)

	fmt.Println("=== 测试主机管理工具 ===")

	// 1. 测试创建主机工具
	fmt.Println("\n1. 测试创建主机...")
	createArgs := map[string]interface{}{
		"name":        "test-server-01",
		"ip_address":  "127.0.0.1",
		"port":        22,
		"username":    "testuser",
		"password":    "testpass",
		"description": "测试服务器",
		"environment": "testing",
	}

	createResult, err := testTool(toolManager, "create_host", createArgs)
	if err != nil {
		fmt.Printf("创建主机失败: %v\n", err)
	} else {
		fmt.Printf("创建主机成功: %+v\n", createResult)
	}

	// 2. 测试列出主机工具
	fmt.Println("\n2. 测试列出主机...")
	listArgs := map[string]interface{}{}
	listResult, err := testTool(toolManager, "list_hosts", listArgs)
	if err != nil {
		fmt.Printf("列出主机失败: %v\n", err)
	} else {
		fmt.Printf("主机列表: %+v\n", listResult)
	}

	// 3. 测试获取主机信息工具
	fmt.Println("\n3. 测试获取主机信息...")
	infoArgs := map[string]interface{}{
		"host_id": float64(1), // JSON数字会被解析为float64
	}
	infoResult, err := testTool(toolManager, "get_host_info", infoArgs)
	if err != nil {
		fmt.Printf("获取主机信息失败: %v\n", err)
	} else {
		fmt.Printf("主机信息: %+v\n", infoResult)
	}

	// 4. 测试连接测试工具
	fmt.Println("\n4. 测试主机连接...")
	testArgs := map[string]interface{}{
		"host_id": float64(1),
	}
	testResult, err := testTool(toolManager, "test_host_connection", testArgs)
	if err != nil {
		fmt.Printf("测试连接失败: %v\n", err)
	} else {
		fmt.Printf("连接测试结果: %+v\n", testResult)
	}

	// 5. 测试命令执行工具（安全命令）
	fmt.Println("\n5. 测试命令执行...")
	cmdArgs := map[string]interface{}{
		"host_id": float64(1),
		"command": "echo 'Hello from AI'",
		"timeout": float64(10),
	}
	cmdResult, err := testTool(toolManager, "execute_command", cmdArgs)
	if err != nil {
		fmt.Printf("命令执行失败: %v\n", err)
	} else {
		fmt.Printf("命令执行结果: %+v\n", cmdResult)
	}

	fmt.Println("\n=== 测试完成 ===")
}

func testTool(toolManager *service.ToolManager, toolName string, args map[string]interface{}) (interface{}, error) {
	// 构造工具调用
	argsJSON, err := json.Marshal(args)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal args: %w", err)
	}

	toolCall := service.ToolCall{
		ID:   fmt.Sprintf("test_%s", toolName),
		Type: "function",
		Function: struct {
			Name      string `json:"name"`
			Arguments string `json:"arguments"`
		}{
			Name:      toolName,
			Arguments: string(argsJSON),
		},
	}

	// 执行工具
	ctx := context.Background()
	result, err := toolManager.ExecuteTool(ctx, toolCall)
	if err != nil {
		return nil, err
	}

	return result, nil
}
