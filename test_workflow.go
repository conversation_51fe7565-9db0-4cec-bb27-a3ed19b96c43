package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

func main() {
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 执行数据库迁移
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建服务
	services, err := service.NewServices(db, cfg, logger)
	if err != nil {
		log.Fatalf("Failed to create services: %v", err)
	}

	// 启动工作流服务
	ctx := context.Background()
	if err := services.Workflow.Start(ctx); err != nil {
		log.Fatalf("Failed to start workflow service: %v", err)
	}

	fmt.Println("🚀 开始测试智能工作流系统...")

	// 测试1: 分析用户意图
	fmt.Println("\n📋 测试1: 分析用户意图")
	testAnalyzeIntent(ctx, services.Workflow)

	// 测试2: 触发主机管理工作流
	fmt.Println("\n📋 测试2: 触发主机管理工作流")
	instanceID := testTriggerHostManagementWorkflow(ctx, services.Workflow)

	// 测试3: 模拟用户输入
	fmt.Println("\n📋 测试3: 模拟用户输入")
	testUserInput(ctx, services.Workflow, instanceID)

	// 测试4: 获取工作流状态
	fmt.Println("\n📋 测试4: 获取工作流状态")
	testGetWorkflowStatus(ctx, services.Workflow, instanceID)

	// 测试5: 获取工作流指标
	fmt.Println("\n📋 测试5: 获取工作流指标")
	testGetWorkflowMetrics(ctx, services.Workflow)

	fmt.Println("\n✅ 所有测试完成!")
}

// 测试分析用户意图
func testAnalyzeIntent(ctx context.Context, workflowService service.WorkflowService) {
	testMessages := []string{
		"我想添加一台新主机",
		"查看所有主机状态",
		"帮我执行一个命令",
		"今天天气怎么样",
		"生成系统监控报表",
	}

	for i, message := range testMessages {
		fmt.Printf("  测试消息 %d: %s\n", i+1, message)

		result, err := workflowService.AnalyzeUserIntent(ctx, "test_session", message)
		if err != nil {
			fmt.Printf("    ❌ 错误: %v\n", err)
			continue
		}

		fmt.Printf("    需要工作流: %v\n", result.NeedsWorkflow)
		if result.NeedsWorkflow {
			fmt.Printf("    工作流类型: %s\n", result.WorkflowType)
			fmt.Printf("    置信度: %.2f\n", result.Confidence)
			fmt.Printf("    建议操作: %s\n", result.SuggestedAction)
		}
		fmt.Println()
	}
}

// 测试触发主机管理工作流
func testTriggerHostManagementWorkflow(ctx context.Context, workflowService service.WorkflowService) string {
	req := &workflow.TriggerWorkflowRequest{
		TriggerType: "intent",
		Intent:      "host_management",
		Category:    "host_management",
		SessionID:   "test_session_001",
		UserID:      1,
		Variables: map[string]interface{}{
			"user_request": "添加新主机",
		},
		Priority: 1,
		Metadata: map[string]interface{}{
			"test": true,
		},
	}

	instance, err := workflowService.TriggerWorkflow(ctx, req)
	if err != nil {
		fmt.Printf("  ❌ 触发工作流失败: %v\n", err)
		return ""
	}

	fmt.Printf("  ✅ 工作流已触发\n")
	fmt.Printf("    实例ID: %s\n", instance.ID)
	fmt.Printf("    状态: %s\n", instance.Status)
	fmt.Printf("    当前步骤: %s\n", instance.CurrentStep)

	return instance.ID
}

// 测试用户输入
func testUserInput(ctx context.Context, workflowService service.WorkflowService, instanceID string) {
	if instanceID == "" {
		fmt.Println("  ⏭️ 跳过测试 - 没有有效的工作流实例")
		return
	}

	// 等待一下让工作流处理
	time.Sleep(2 * time.Second)

	// 模拟用户提供主机信息
	hostInfo := map[string]interface{}{
		"host_name":   "test-server-01",
		"ip_address":  "*************",
		"ssh_port":    22,
		"username":    "admin",
		"description": "测试服务器",
	}

	hostInfoJSON, _ := json.Marshal(hostInfo)
	userInput := string(hostInfoJSON)

	fmt.Printf("  发送用户输入: %s\n", userInput)

	response, err := workflowService.ProcessUserInput(ctx, instanceID, userInput)
	if err != nil {
		fmt.Printf("  ❌ 处理用户输入失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 用户输入已处理\n")
	fmt.Printf("    状态: %s\n", response.Status)
	fmt.Printf("    消息: %s\n", response.Message)
}

// 测试获取工作流状态
func testGetWorkflowStatus(ctx context.Context, workflowService service.WorkflowService, instanceID string) {
	if instanceID == "" {
		fmt.Println("  ⏭️ 跳过测试 - 没有有效的工作流实例")
		return
	}

	instance, err := workflowService.GetWorkflowInstance(instanceID)
	if err != nil {
		fmt.Printf("  ❌ 获取工作流状态失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 工作流状态获取成功\n")
	fmt.Printf("    实例ID: %s\n", instance.ID)
	fmt.Printf("    定义ID: %s\n", instance.DefinitionID)
	fmt.Printf("    状态: %s\n", instance.Status)
	fmt.Printf("    当前步骤: %s\n", instance.CurrentStep)
	fmt.Printf("    开始时间: %s\n", instance.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("    最后活动: %s\n", instance.LastActivity.Format("2006-01-02 15:04:05"))

	if len(instance.Variables) > 0 {
		fmt.Printf("    变量: %d 个\n", len(instance.Variables))
	}

	if len(instance.CollectedData) > 0 {
		fmt.Printf("    收集的数据: %d 个\n", len(instance.CollectedData))
	}

	if len(instance.ExecutionPath) > 0 {
		fmt.Printf("    执行路径: %v\n", instance.ExecutionPath)
	}
}

// 测试获取工作流指标
func testGetWorkflowMetrics(ctx context.Context, workflowService service.WorkflowService) {
	metrics, err := workflowService.GetWorkflowMetrics()
	if err != nil {
		fmt.Printf("  ❌ 获取工作流指标失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 工作流指标获取成功\n")
	fmt.Printf("    总实例数: %d\n", metrics.TotalInstances)
	fmt.Printf("    运行中实例: %d\n", metrics.RunningInstances)
	fmt.Printf("    今日完成: %d\n", metrics.CompletedToday)
	fmt.Printf("    今日失败: %d\n", metrics.FailedToday)
	fmt.Printf("    成功率: %.2f%%\n", metrics.SuccessRate*100)
}

// 测试生成工作流引导
func testGenerateGuidance(ctx context.Context, workflowService service.WorkflowService) {
	guidance, err := workflowService.GenerateGuidance(ctx, "test_session_001")
	if err != nil {
		fmt.Printf("  ❌ 生成工作流引导失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 工作流引导生成成功\n")
	fmt.Printf("    消息: %s\n", guidance.Message)
	fmt.Printf("    建议: %v\n", guidance.Suggestions)
	fmt.Printf("    下一步操作: %s\n", guidance.NextAction)
	fmt.Printf("    有活跃工作流: %v\n", guidance.HasActiveWorkflow)
	if guidance.HasActiveWorkflow {
		fmt.Printf("    活跃工作流ID: %s\n", guidance.ActiveWorkflowID)
	}
}

// 测试获取活跃工作流
func testGetActiveWorkflows(ctx context.Context, workflowService service.WorkflowService) {
	workflows, err := workflowService.GetActiveWorkflows("test_session_001")
	if err != nil {
		fmt.Printf("  ❌ 获取活跃工作流失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 活跃工作流获取成功\n")
	fmt.Printf("    活跃工作流数量: %d\n", len(workflows))

	for i, workflow := range workflows {
		fmt.Printf("    工作流 %d:\n", i+1)
		fmt.Printf("      ID: %s\n", workflow.ID)
		fmt.Printf("      定义ID: %s\n", workflow.DefinitionID)
		fmt.Printf("      状态: %s\n", workflow.Status)
		fmt.Printf("      当前步骤: %s\n", workflow.CurrentStep)
	}
}
