package ai

import (
	"context"
	"fmt"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

// hostStatusDiagnosisHandler 主机状态诊断处理器
type hostStatusDiagnosisHandler struct {
	deepseekService *service.DeepSeekService
	hostService     workflow.HostServiceInterface
	logger          *logrus.Logger
}

// NewHostStatusDiagnosisHandler 创建主机状态诊断处理器
func NewHostStatusDiagnosisHandler(
	deepseekService *service.DeepSeekService,
	hostService workflow.HostServiceInterface,
	logger *logrus.Logger,
) ScenarioHandler {
	return &hostStatusDiagnosisHandler{
		deepseekService: deepseekService,
		hostService:     hostService,
		logger:          logger,
	}
}

// CanHandle 检查是否能处理此场景
func (hsdh *hostStatusDiagnosisHandler) CanHandle(intent string, entities map[string]interface{}) bool {
	if intent != IntentHostStatusDiagnosis {
		return false
	}

	// 必须有IP地址或主机名
	_, hasIP := entities[EntityIPAddress]
	_, hasHostname := entities[EntityHostname]

	return hasIP || hasHostname
}

// GenerateCommands 生成诊断命令
func (hsdh *hostStatusDiagnosisHandler) GenerateCommands(ctx context.Context, classification *ClassificationResult, message string) ([]CommandSequence, error) {
	targetHost := hsdh.extractTargetHost(classification.Entities)
	if targetHost == "" {
		return nil, fmt.Errorf("无法提取目标主机信息")
	}

	hsdh.logger.WithFields(logrus.Fields{
		"target_host": targetHost,
		"intent":      classification.Intent,
	}).Info("生成主机状态诊断命令")

	// 生成主机状态诊断命令序列
	return hsdh.generateHostStatusDiagnosisCommands(targetHost), nil
}

// GenerateExecutionPlan 生成执行计划
func (hsdh *hostStatusDiagnosisHandler) GenerateExecutionPlan(commands []CommandSequence) *ExecutionPlan {
	return &ExecutionPlan{
		Strategy:      "sequential",
		Timeout:       45, // 45秒总超时
		ErrorHandling: "continue_on_error",
		Prerequisites: []string{"target_host_available"},
		Variables: map[string]string{
			"max_retries":    "2",
			"retry_interval": "3",
		},
	}
}

// GetDescription 获取处理器描述
func (hsdh *hostStatusDiagnosisHandler) GetDescription() string {
	return "Host status diagnosis handler for checking host connectivity and status issues"
}

// extractTargetHost 提取目标主机
func (hsdh *hostStatusDiagnosisHandler) extractTargetHost(entities map[string]interface{}) string {
	if ip, exists := entities[EntityIPAddress]; exists {
		if ipStr, ok := ip.(string); ok {
			return ipStr
		}
	}

	if hostname, exists := entities[EntityHostname]; exists {
		if hostnameStr, ok := hostname.(string); ok {
			return hostnameStr
		}
	}

	return ""
}

// generateHostStatusDiagnosisCommands 生成主机状态诊断命令
func (hsdh *hostStatusDiagnosisHandler) generateHostStatusDiagnosisCommands(targetHost string) []CommandSequence {
	return []CommandSequence{
		{
			Step:        1,
			Command:     fmt.Sprintf("ping -c 3 %s", targetHost),
			Description: "测试网络连通性",
			Parameters:  map[string]string{"host": targetHost, "count": "3"},
			Timeout:     10,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        2,
			Command:     fmt.Sprintf("nc -zv %s 22", targetHost),
			Description: "检查SSH端口(22)状态",
			Parameters:  map[string]string{"host": targetHost, "port": "22"},
			Timeout:     8,
			Required:    true,
			OnError:     "continue",
		},
		{
			Step:        3,
			Command:     fmt.Sprintf("ssh -o ConnectTimeout=5 -o BatchMode=yes %s 'echo \"主机在线检测\"'", targetHost),
			Description: "尝试SSH连接测试",
			Parameters:  map[string]string{"host": targetHost, "test_type": "ssh_connect"},
			Timeout:     10,
			Required:    false,
			OnError:     "continue",
		},
		{
			Step:        4,
			Command:     fmt.Sprintf("nmap -p 22,80,443 %s", targetHost),
			Description: "扫描常用端口状态",
			Parameters:  map[string]string{"host": targetHost, "ports": "22,80,443"},
			Timeout:     15,
			Required:    false,
			OnError:     "continue",
		},
	}
}

// ExecuteWithHostService 使用主机服务执行诊断
func (hsdh *hostStatusDiagnosisHandler) ExecuteWithHostService(ctx context.Context, targetHost string) (*HostDiagnosisResult, error) {
	// 首先尝试通过IP地址查找主机
	hostID, err := hsdh.findHostByIP(targetHost)
	if err != nil {
		hsdh.logger.WithError(err).Warn("无法找到主机记录，将执行基础网络诊断")
		return hsdh.executeBasicDiagnosis(ctx, targetHost)
	}

	// 使用主机服务进行连接测试（workflow接口只返回error）
	err = hsdh.hostService.TestConnection(hostID)

	// 构建诊断结果
	status := "offline"
	message := "主机离线"
	details := []string{}

	if err == nil {
		status = "online"
		message = "主机在线正常"
		details = append(details, "连接成功，主机状态正常")
	} else {
		details = append(details, fmt.Sprintf("连接失败: %s", err.Error()))
		details = append(details, "可能原因: SSH服务未启动、防火墙阻止、网络不通")
	}

	return &HostDiagnosisResult{
		HostIP:    targetHost,
		Status:    status,
		Message:   message,
		Details:   details,
		Timestamp: "now",
	}, nil
}

// findHostByIP 通过IP地址查找主机ID
func (hsdh *hostStatusDiagnosisHandler) findHostByIP(ipAddress string) (int64, error) {
	// 这里需要实现通过IP地址查找主机ID的逻辑
	// 暂时返回错误，表示未找到
	return 0, fmt.Errorf("host not found for IP: %s", ipAddress)
}

// executeBasicDiagnosis 执行基础网络诊断
func (hsdh *hostStatusDiagnosisHandler) executeBasicDiagnosis(ctx context.Context, targetHost string) (*HostDiagnosisResult, error) {
	return &HostDiagnosisResult{
		HostIP:    targetHost,
		Status:    "unknown",
		Message:   "无法找到主机记录，建议先添加主机到系统中",
		Details:   []string{"主机未在系统中注册", "请使用'添加主机'功能先添加此主机"},
		Timestamp: "now",
	}, nil
}

// HostDiagnosisResult 主机诊断结果
type HostDiagnosisResult struct {
	HostIP    string   `json:"host_ip"`
	Status    string   `json:"status"`
	Message   string   `json:"message"`
	Details   []string `json:"details"`
	Timestamp string   `json:"timestamp"`
}
