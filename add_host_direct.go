package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

// 主机添加响应结构
type HostResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		ID        int64  `json:"id"`
		Name      string `json:"name"`
		IPAddress string `json:"ip_address"`
		Status    string `json:"status"`
	} `json:"data"`
}

// 连接测试响应结构
type TestResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Success  bool   `json:"success"`
		Message  string `json:"message"`
		Duration int64  `json:"duration"`
	} `json:"data"`
}

func main() {
	fmt.Println("🚀 AI运维平台 - 一键式主机添加工具")
	fmt.Println(strings.Repeat("=", 50))

	// 解析主机信息
	ip := "**************"
	username := "root"
	password := "1qaz#EDC"

	fmt.Printf("📋 准备添加主机:\n")
	fmt.Printf("   IP地址: %s\n", ip)
	fmt.Printf("   用户名: %s\n", username)
	fmt.Printf("   密码: %s\n", strings.Repeat("*", len(password)))
	fmt.Println()

	// 步骤1: 网络连通性检查
	fmt.Println("🔍 步骤1: 检查网络连通性...")
	if !checkConnectivity(ip) {
		fmt.Println("❌ 网络连通性检查失败，请检查网络设置")
		return
	}
	fmt.Println("✅ 网络连通性检查通过")

	// 步骤2: 端口检查
	fmt.Println("🔍 步骤2: 检查SSH端口...")
	if !checkPort(ip, 22) {
		fmt.Println("❌ SSH端口(22)不可达，请检查防火墙设置")
		return
	}
	fmt.Println("✅ SSH端口检查通过")

	// 步骤3: 添加主机
	fmt.Println("🔍 步骤3: 添加主机到管理系统...")
	hostID, err := addHost(ip, username, password)
	if err != nil {
		fmt.Printf("❌ 主机添加失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 主机添加成功 (ID: %d)\n", hostID)

	// 步骤4: 连接测试
	fmt.Println("🔍 步骤4: 执行连接测试...")
	if !testConnection(hostID) {
		fmt.Println("⚠️  连接测试失败，但主机已添加到系统中")
		return
	}
	fmt.Println("✅ 连接测试成功")

	// 完成
	fmt.Println()
	fmt.Println("🎉 主机添加完成！")
	fmt.Printf("📊 您现在可以在Web界面查看和管理主机 %s\n", ip)
	fmt.Println("🌐 访问: http://localhost:8766/hosts")
}

// checkConnectivity 检查网络连通性
func checkConnectivity(ip string) bool {
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("ping", "-n", "1", "-w", "3000", ip)
	} else {
		cmd = exec.Command("ping", "-c", "1", "-W", "3", ip)
	}

	err := cmd.Run()
	return err == nil
}

// checkPort 检查端口连通性
func checkPort(ip string, port int) bool {
	timeout := time.Second * 3
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", ip, port), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// addHost 添加主机到系统
func addHost(ip, username, password string) (int64, error) {
	// 构建请求数据
	data := map[string]interface{}{
		"name":               fmt.Sprintf("主机-%s", ip),
		"ip_address":         ip,
		"port":               22,
		"username":           username,
		"password":           password,
		"description":        fmt.Sprintf("通过一键工具添加 - %s", time.Now().Format("2006-01-02 15:04:05")),
		"environment":        "production",
		"monitoring_enabled": true,
		"backup_enabled":     false,
	}

	// 发送请求
	jsonData, _ := json.Marshal(data)
	client := &http.Client{Timeout: 30 * time.Second}
	req, _ := http.NewRequest("POST", "http://127.0.0.1:8766/api/v1/hosts", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return 0, fmt.Errorf("网络请求失败: %v", err)
	}
	defer resp.Body.Close()

	responseBody, _ := io.ReadAll(resp.Body)

	if resp.StatusCode == 201 {
		var result HostResponse
		if err := json.Unmarshal(responseBody, &result); err != nil {
			return 0, fmt.Errorf("解析响应失败: %v", err)
		}
		return result.Data.ID, nil
	} else {
		var errorResult map[string]interface{}
		json.Unmarshal(responseBody, &errorResult)
		return 0, fmt.Errorf("添加失败: %s", errorResult["message"])
	}
}

// testConnection 测试主机连接
func testConnection(hostID int64) bool {
	client := &http.Client{Timeout: 30 * time.Second}
	req, _ := http.NewRequest("POST", fmt.Sprintf("http://127.0.0.1:8766/api/v1/hosts/%d/test", hostID), nil)

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   连接测试请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	responseBody, _ := io.ReadAll(resp.Body)

	if resp.StatusCode == 200 {
		var result TestResponse
		if err := json.Unmarshal(responseBody, &result); err != nil {
			fmt.Printf("   解析测试响应失败: %v\n", err)
			return false
		}

		if result.Data.Success {
			fmt.Printf("   连接耗时: %dms\n", result.Data.Duration)
			return true
		} else {
			fmt.Printf("   连接失败: %s\n", result.Data.Message)
			return false
		}
	} else {
		var errorResult map[string]interface{}
		json.Unmarshal(responseBody, &errorResult)
		fmt.Printf("   测试失败: %s\n", errorResult["message"])
		return false
	}
}
