package main

import (
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/logger"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	loggerInstance := logger.New(cfg.Log)

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("=== 测试监控告警系统 ===")

	// 1. 测试监控数据查询
	fmt.Println("\n1. 测试监控数据查询...")

	// 查询最近的监控数据
	var metricData []service.MetricData
	if err := db.Order("timestamp DESC").Limit(5).Find(&metricData).Error; err != nil {
		fmt.Printf("查询监控数据失败: %v\n", err)
	} else {
		fmt.Printf("找到 %d 条监控数据:\n", len(metricData))
		for _, data := range metricData {
			fmt.Printf("- 主机ID: %d, 指标类型: %s, 值: %.2f, 时间: %s\n",
				data.HostID, data.MetricType, data.Value, data.Timestamp.Format("2006-01-02 15:04:05"))
		}
	}

	// 2. 测试告警数据查询
	fmt.Println("\n2. 测试告警数据查询...")

	var alerts []service.Alert
	if err := db.Order("created_at DESC").Limit(5).Find(&alerts).Error; err != nil {
		fmt.Printf("查询告警数据失败: %v\n", err)
	} else {
		fmt.Printf("找到 %d 条告警数据:\n", len(alerts))
		for _, alert := range alerts {
			fmt.Printf("- 告警ID: %s, 主机ID: %d, 级别: %s, 标题: %s, 状态: %s\n",
				alert.ID, alert.HostID, alert.Level, alert.Title, alert.Status)
		}
	}

	// 3. 测试主机状态
	fmt.Println("\n3. 测试主机状态...")

	// 创建主机服务
	hostService := service.NewHostService(db, cfg, loggerInstance)

	// 获取所有主机
	hosts, err := hostService.ListHosts(&model.HostListQuery{
		Page:  1,
		Limit: 10,
	})
	if err != nil {
		fmt.Printf("获取主机列表失败: %v\n", err)
	} else {
		fmt.Printf("找到 %d 台主机:\n", len(hosts.Hosts))
		for _, host := range hosts.Hosts {
			fmt.Printf("- 主机: %s (%s), 状态: %s, 最后连接: %v\n",
				host.Name, host.IPAddress, host.Status, host.LastConnected)
		}
	}

	// 4. 测试创建模拟告警
	fmt.Println("\n4. 测试创建模拟告警...")

	// 创建告警服务
	alertService := service.NewAlertService(db, loggerInstance)

	// 创建一个测试告警
	hostID := int64(1)
	testAlert := &model.AlertCreateRequest{
		Title:   "测试告警 - CPU使用率过高",
		Message: "主机 test-server-01 的CPU使用率达到85%，超过阈值80%",
		Level:   "warning",
		Source:  "monitoring",
		HostID:  &hostID,
	}

	alert, err := alertService.CreateAlert(testAlert)
	if err != nil {
		fmt.Printf("创建测试告警失败: %v\n", err)
	} else {
		fmt.Printf("成功创建测试告警: ID=%d, 标题=%s\n", alert.ID, alert.Title)
	}

	// 5. 测试告警统计
	fmt.Println("\n5. 测试告警统计...")

	summary, err := alertService.GetAlertSummary()
	if err != nil {
		fmt.Printf("获取告警统计失败: %v\n", err)
	} else {
		fmt.Printf("告警统计:\n")
		fmt.Printf("- 总告警数: %d\n", summary.Total)
		fmt.Printf("- 开放告警: %d\n", summary.Open)
		fmt.Printf("- 已确认告警: %d\n", summary.Acknowledged)
		fmt.Printf("- 已解决告警: %d\n", summary.Resolved)
		// 注意：AlertSummary可能没有Closed字段，跳过这一行
		fmt.Printf("- 严重告警: %d\n", summary.Critical)
		fmt.Printf("- 警告告警: %d\n", summary.Warning)
		fmt.Printf("- 信息告警: %d\n", summary.Info)
	}

	// 6. 等待一段时间观察监控数据收集
	fmt.Println("\n6. 等待30秒观察监控数据收集...")

	initialCount := len(metricData)
	time.Sleep(30 * time.Second)

	// 再次查询监控数据
	var newMetricData []service.MetricData
	if err := db.Order("timestamp DESC").Limit(10).Find(&newMetricData).Error; err != nil {
		fmt.Printf("查询新监控数据失败: %v\n", err)
	} else {
		fmt.Printf("现在有 %d 条监控数据 (之前: %d)\n", len(newMetricData), initialCount)
		if len(newMetricData) > initialCount {
			fmt.Println("✅ 监控数据收集正在正常工作！")
			fmt.Println("最新的监控数据:")
			for i, data := range newMetricData[:3] {
				fmt.Printf("%d. 主机ID: %d, 指标: %s, 值: %.2f, 时间: %s\n",
					i+1, data.HostID, data.MetricType, data.Value, data.Timestamp.Format("15:04:05"))
			}
		} else {
			fmt.Println("⚠️ 监控数据收集可能没有正常工作")
		}
	}

	fmt.Println("\n=== 监控告警系统测试完成 ===")
}
