package main

import (
	"context"
	"fmt"
	"log"

	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
)

func main() {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	mockAI := service.NewMockAIService(logger)

	testCases := []string{
		"添加主机 192.168.119.80 root 1qaz#EDC",
		"192.168.119.80 root 1qaz#EDC",
		"主机添加",
		"新增主机 10.0.0.100 admin password",
		"查看主机列表",
		"主机状态",
		"CPU使用率",
		"你好",
	}

	fmt.Println("=== 测试模拟AI服务意图识别 ===")

	for i, testCase := range testCases {
		fmt.Printf("\n%d. 测试输入: %s\n", i+1, testCase)

		result, err := mockAI.RecognizeIntent(context.Background(), testCase)
		if err != nil {
			log.Printf("错误: %v", err)
			continue
		}

		fmt.Printf("   意图类型: %s\n", result.Type)
		fmt.Printf("   置信度: %.2f\n", result.Confidence)
		fmt.Printf("   参数: %+v\n", result.Parameters)
	}
}
