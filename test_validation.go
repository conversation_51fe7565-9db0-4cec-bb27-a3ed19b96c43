package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	baseURL := "http://localhost:8766/api/v1"

	fmt.Println("=== 测试验证逻辑 ===")

	// 测试无认证方式（应该失败）
	fmt.Println("\n测试添加主机（无认证方式）...")
	data := map[string]interface{}{
		"name":        "test-validation-host",
		"ip_address":  "*************",
		"port":        22,
		"username":    "testuser",
		"description": "测试验证逻辑主机（应该失败）",
		"environment": "testing",
	}

	jsonData, _ := json.Marshal(data)
	fmt.Printf("发送数据: %s\n", jsonData)

	client := &http.Client{Timeout: 10 * time.Second}
	req, _ := http.NewRequest("POST", baseURL+"/hosts", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	responseBody, _ := io.ReadAll(resp.Body)
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应: %s\n", responseBody)
}
