package main

import (
	"fmt"
	"regexp"
	"strings"
)

// 测试意图识别优化效果
func main() {
	fmt.Println("=== AI运维管理平台意图识别优化测试 ===")

	// 创建测试用例
	testCases := []TestCase{
		{
			Name:           "主机状态诊断测试1",
			Input:          "查看************** 为啥是离线状态",
			ExpectedIntent: "host_status_diagnosis",
			Description:    "测试主机离线状态诊断意图识别",
		},
		{
			Name:           "主机状态诊断测试2",
			Input:          "*************为什么连不上",
			ExpectedIntent: "host_status_diagnosis",
			Description:    "测试主机连接问题诊断意图识别",
		},
		{
			Name:           "SSH连接测试1",
			Input:          "ssh连接下************** 看看报什么错误",
			ExpectedIntent: "ssh_connection_test",
			Description:    "测试SSH连接错误诊断意图识别",
		},
		{
			Name:           "SSH连接测试2",
			Input:          "ssh连接************测试一下",
			ExpectedIntent: "ssh_connection_test",
			Description:    "测试SSH连接测试意图识别",
		},
		{
			Name:           "连接诊断测试",
			Input:          "检查*************连接问题",
			ExpectedIntent: "connection_diagnosis",
			Description:    "测试连接诊断意图识别",
		},
		{
			Name:           "主机管理测试",
			Input:          "添加主机 ************ root password123",
			ExpectedIntent: "host_management",
			Description:    "测试主机添加意图识别",
		},
	}

	// 运行测试
	runIntentRecognitionTests(testCases)

	// 测试智能执行功能
	fmt.Println("\n=== 智能执行功能测试 ===")
	testSmartExecution()

	fmt.Println("\n=== 测试完成 ===")
}

// TestCase 测试用例
type TestCase struct {
	Name           string
	Input          string
	ExpectedIntent string
	Description    string
}

// runIntentRecognitionTests 运行意图识别测试
func runIntentRecognitionTests(testCases []TestCase) {
	fmt.Println("\n--- 意图识别测试 ---")

	// 创建模拟的意图分类器
	// logger := logrus.New()
	// logger.SetLevel(logrus.InfoLevel)

	// 模拟测试每个用例
	for i, testCase := range testCases {
		fmt.Printf("\n%d. %s\n", i+1, testCase.Name)
		fmt.Printf("   输入: %s\n", testCase.Input)
		fmt.Printf("   描述: %s\n", testCase.Description)

		// 模拟意图识别结果
		recognizedIntent := simulateIntentRecognition(testCase.Input)

		fmt.Printf("   识别结果: %s\n", recognizedIntent.Intent)
		fmt.Printf("   置信度: %.2f\n", recognizedIntent.Confidence)

		if recognizedIntent.Intent == testCase.ExpectedIntent {
			fmt.Printf("   ✅ 测试通过\n")
		} else {
			fmt.Printf("   ❌ 测试失败 (期望: %s, 实际: %s)\n", testCase.ExpectedIntent, recognizedIntent.Intent)
		}

		// 显示提取的实体
		if len(recognizedIntent.Entities) > 0 {
			fmt.Printf("   提取实体: %v\n", recognizedIntent.Entities)
		}
	}
}

// simulateIntentRecognition 模拟意图识别
func simulateIntentRecognition(input string) *MockIntentResult {
	input = strings.ToLower(input)

	// 主机状态诊断规则
	if (strings.Contains(input, "查看") || strings.Contains(input, "检查")) &&
		(strings.Contains(input, "为啥") || strings.Contains(input, "为什么")) &&
		(strings.Contains(input, "离线") || strings.Contains(input, "连不上")) {
		return &MockIntentResult{
			Intent:     "host_status_diagnosis",
			Confidence: 0.95,
			Entities:   extractIPFromInput(input),
		}
	}

	// SSH连接测试规则
	if strings.Contains(input, "ssh连接") &&
		(strings.Contains(input, "看看") || strings.Contains(input, "测试")) {
		return &MockIntentResult{
			Intent:     "ssh_connection_test",
			Confidence: 0.92,
			Entities:   extractIPFromInput(input),
		}
	}

	// 连接诊断规则
	if strings.Contains(input, "检查") && strings.Contains(input, "连接") {
		return &MockIntentResult{
			Intent:     "connection_diagnosis",
			Confidence: 0.88,
			Entities:   extractIPFromInput(input),
		}
	}

	// 主机管理规则
	if strings.Contains(input, "添加主机") {
		return &MockIntentResult{
			Intent:     "host_management",
			Confidence: 0.96,
			Entities:   extractHostInfoFromInput(input),
		}
	}

	// 默认通用对话
	return &MockIntentResult{
		Intent:     "general_chat",
		Confidence: 0.5,
		Entities:   make(map[string]interface{}),
	}
}

// extractIPFromInput 从输入中提取IP地址
func extractIPFromInput(input string) map[string]interface{} {
	// 简单的IP地址正则匹配
	ipPattern := `\b(?:\d{1,3}\.){3}\d{1,3}\b`
	re := regexp.MustCompile(ipPattern)
	matches := re.FindAllString(input, -1)

	entities := make(map[string]interface{})
	if len(matches) > 0 {
		entities["ip_address"] = matches[0]
	}

	return entities
}

// extractHostInfoFromInput 从输入中提取主机信息
func extractHostInfoFromInput(input string) map[string]interface{} {
	entities := make(map[string]interface{})

	// 提取IP地址
	ipPattern := `\b(?:\d{1,3}\.){3}\d{1,3}\b`
	re := regexp.MustCompile(ipPattern)
	ipMatches := re.FindAllString(input, -1)
	if len(ipMatches) > 0 {
		entities["ip_address"] = ipMatches[0]
	}

	// 简单的用户名和密码提取（实际应用中需要更复杂的逻辑）
	parts := strings.Fields(input)
	if len(parts) >= 4 {
		entities["username"] = parts[len(parts)-2]
		entities["password"] = parts[len(parts)-1]
	}

	return entities
}

// testSmartExecution 测试智能执行功能
func testSmartExecution() {
	smartExecutionCases := []SmartExecutionCase{
		{
			Intent: &MockIntentResult{
				Intent:     "host_status_diagnosis",
				Confidence: 0.95,
				Entities:   map[string]interface{}{"ip_address": "**************"},
			},
			ExpectedExecution: true,
			ExpectedMessage:   "主机状态诊断",
		},
		{
			Intent: &MockIntentResult{
				Intent:     "ssh_connection_test",
				Confidence: 0.92,
				Entities:   map[string]interface{}{"ip_address": "**************"},
			},
			ExpectedExecution: true,
			ExpectedMessage:   "SSH连接测试",
		},
		{
			Intent: &MockIntentResult{
				Intent:     "general_chat",
				Confidence: 0.6,
				Entities:   make(map[string]interface{}),
			},
			ExpectedExecution: false,
			ExpectedMessage:   "通用对话",
		},
	}

	for i, testCase := range smartExecutionCases {
		fmt.Printf("\n%d. 智能执行测试 - %s\n", i+1, testCase.Intent.Intent)

		shouldExecute := shouldAutoExecute(testCase.Intent)
		fmt.Printf("   应该自动执行: %v\n", shouldExecute)

		if shouldExecute == testCase.ExpectedExecution {
			fmt.Printf("   ✅ 执行判断正确\n")
		} else {
			fmt.Printf("   ❌ 执行判断错误\n")
		}

		if shouldExecute {
			executionResult := simulateSmartExecution(testCase.Intent)
			fmt.Printf("   执行结果: %s\n", executionResult)
		}
	}
}

// shouldAutoExecute 判断是否应该自动执行（模拟）
func shouldAutoExecute(intent *MockIntentResult) bool {
	autoExecuteIntents := []string{
		"host_status_diagnosis",
		"ssh_connection_test",
		"connection_diagnosis",
	}

	for _, autoIntent := range autoExecuteIntents {
		if intent.Intent == autoIntent && intent.Confidence >= 0.8 {
			return true
		}
	}

	return false
}

// simulateSmartExecution 模拟智能执行
func simulateSmartExecution(intent *MockIntentResult) string {
	switch intent.Intent {
	case "host_status_diagnosis":
		if ip, exists := intent.Entities["ip_address"]; exists {
			return fmt.Sprintf("✅ 已自动诊断主机 %v 状态：主机当前离线，可能原因：网络不通或SSH服务未启动", ip)
		}
		return "✅ 主机状态诊断完成"

	case "ssh_connection_test":
		if ip, exists := intent.Entities["ip_address"]; exists {
			return fmt.Sprintf("✅ 已自动测试SSH连接到 %v：连接失败 - Connection refused (端口22被拒绝)", ip)
		}
		return "✅ SSH连接测试完成"

	case "connection_diagnosis":
		return "✅ 综合连接诊断完成：网络连通性正常，SSH服务异常"

	default:
		return "执行完成"
	}
}

// MockIntentResult 模拟意图结果
type MockIntentResult struct {
	Intent     string                 `json:"intent"`
	Confidence float64                `json:"confidence"`
	Entities   map[string]interface{} `json:"entities"`
}

// SmartExecutionCase 智能执行测试用例
type SmartExecutionCase struct {
	Intent            *MockIntentResult
	ExpectedExecution bool
	ExpectedMessage   string
}
