package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/logger"
	"aiops-platform/internal/service"
)

func main() {
	fmt.Println("🔧 测试CreateHostTool工具...")

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logInstance := logger.New(cfg.Log)

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 自动迁移数据库
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化服务
	services, err := service.NewServices(cfg, db, logInstance)
	if err != nil {
		log.Fatalf("Failed to initialize services: %v", err)
	}

	// 创建工具管理器
	toolManager := service.NewToolManager(logInstance, services.Host)

	// 获取可用工具
	tools := toolManager.GetAvailableTools()
	fmt.Printf("✅ 可用工具数量: %d\n", len(tools))

	// 查找create_host工具
	var createHostTool *service.Tool
	for _, tool := range tools {
		if tool.Function.Name == "create_host" {
			createHostTool = &tool
			break
		}
	}

	if createHostTool == nil {
		fmt.Println("❌ 未找到create_host工具")
		return
	}

	fmt.Println("✅ 找到create_host工具")
	fmt.Printf("   描述: %s\n", createHostTool.Function.Description)

	// 测试工具调用
	ctx := context.Background()
	toolCall := service.ToolCall{
		ID:   "test_call_1",
		Type: "function",
		Function: struct {
			Name      string `json:"name"`
			Arguments string `json:"arguments"`
		}{
			Name: "create_host",
			Arguments: `{
				"name": "test-server-01",
				"ip_address": "*************",
				"username": "root",
				"password": "test123",
				"description": "测试服务器",
				"environment": "development"
			}`,
		},
	}

	fmt.Println("🚀 执行create_host工具...")
	result, err := toolManager.ExecuteTool(ctx, toolCall)
	if err != nil {
		fmt.Printf("❌ 工具执行失败: %v\n", err)
		return
	}

	// 格式化输出结果
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("✅ 工具执行成功:\n%s\n", string(resultJSON))

	fmt.Println("\n🎯 测试总结:")
	fmt.Println("✅ CreateHostTool已成功注册")
	fmt.Println("✅ 工具可以正常执行")
	fmt.Println("✅ AI现在可以处理'添加主机账号'请求")
}
