package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/agent"
	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
)

// FallbackHandler 降级处理器
type FallbackHandler struct {
	agentRegistry   *agent.AgentRegistry
	logger          *logrus.Logger
	config          *FallbackConfig
	fallbackRules   []*FallbackRule
	errorPatterns   map[string]*ErrorPattern
}

// FallbackConfig 降级配置
type FallbackConfig struct {
	EnableFallback       bool          `json:"enable_fallback"`
	MaxFallbackAttempts  int           `json:"max_fallback_attempts"`
	FallbackTimeout      time.Duration `json:"fallback_timeout"`
	EnableErrorLearning  bool          `json:"enable_error_learning"`
	LogFallbackEvents    bool          `json:"log_fallback_events"`
}

// FallbackRule 降级规则
type FallbackRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Condition   *FallbackCondition     `json:"condition"`
	Action      *FallbackAction        `json:"action"`
	Priority    int                    `json:"priority"`
	Enabled     bool                   `json:"enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// FallbackCondition 降级条件
type FallbackCondition struct {
	ErrorType       string   `json:"error_type"`
	ErrorPatterns   []string `json:"error_patterns"`
	AgentIDs        []string `json:"agent_ids"`
	Capabilities    []string `json:"capabilities"`
	ConfidenceBelow float64  `json:"confidence_below"`
	TimeoutAfter    string   `json:"timeout_after"`
}

// FallbackAction 降级动作
type FallbackAction struct {
	Type            string                 `json:"type"`
	AlternativeAgent string                 `json:"alternative_agent,omitempty"`
	SimplifiedTask  *SimplifiedTask        `json:"simplified_task,omitempty"`
	ManualResponse  string                 `json:"manual_response,omitempty"`
	RetryConfig     *RetryConfig           `json:"retry_config,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// SimplifiedTask 简化任务
type SimplifiedTask struct {
	AgentID     string                 `json:"agent_id"`
	Capability  string                 `json:"capability"`
	Parameters  map[string]interface{} `json:"parameters"`
	Description string                 `json:"description"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts   int           `json:"max_attempts"`
	RetryInterval time.Duration `json:"retry_interval"`
	BackoffFactor float64       `json:"backoff_factor"`
}

// ErrorPattern 错误模式
type ErrorPattern struct {
	Pattern     string `json:"pattern"`
	Category    string `json:"category"`
	Severity    string `json:"severity"`
	Description string `json:"description"`
	Solution    string `json:"solution"`
}

// FallbackResult 降级结果
type FallbackResult struct {
	Success         bool                   `json:"success"`
	FallbackType    string                 `json:"fallback_type"`
	OriginalError   string                 `json:"original_error"`
	FallbackAction  string                 `json:"fallback_action"`
	Result          *AgentDispatchResult   `json:"result,omitempty"`
	Message         string                 `json:"message"`
	Suggestions     []string               `json:"suggestions"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// NewFallbackHandler 创建降级处理器
func NewFallbackHandler(
	agentRegistry *agent.AgentRegistry,
	logger *logrus.Logger,
) *FallbackHandler {
	config := &FallbackConfig{
		EnableFallback:      true,
		MaxFallbackAttempts: 3,
		FallbackTimeout:     30 * time.Second,
		EnableErrorLearning: true,
		LogFallbackEvents:   true,
	}

	handler := &FallbackHandler{
		agentRegistry: agentRegistry,
		logger:        logger,
		config:        config,
		fallbackRules: make([]*FallbackRule, 0),
		errorPatterns: make(map[string]*ErrorPattern),
	}

	// 初始化默认规则和错误模式
	handler.initializeDefaultRules()
	handler.initializeErrorPatterns()

	return handler
}

// HandleFallback 处理降级
func (fh *FallbackHandler) HandleFallback(
	ctx context.Context,
	originalRequest *AgentDispatchRequest,
	originalError error,
	dispatchResult *AgentDispatchResult,
) (*FallbackResult, error) {
	if !fh.config.EnableFallback {
		return &FallbackResult{
			Success:       false,
			FallbackType:  "disabled",
			OriginalError: originalError.Error(),
			Message:       "降级处理已禁用",
		}, nil
	}

	fh.logger.WithFields(logrus.Fields{
		"user_message":    originalRequest.UserMessage,
		"session_id":      originalRequest.SessionID,
		"original_error":  originalError.Error(),
	}).Info("Starting fallback handling")

	// 1. 分析错误类型
	errorCategory := fh.analyzeError(originalError)

	// 2. 查找匹配的降级规则
	rule := fh.findMatchingRule(originalRequest, originalError, dispatchResult)
	if rule == nil {
		return fh.createDefaultFallback(originalRequest, originalError)
	}

	// 3. 执行降级动作
	result, err := fh.executeFallbackAction(ctx, originalRequest, rule, errorCategory)
	if err != nil {
		fh.logger.WithError(err).Error("Failed to execute fallback action")
		return fh.createDefaultFallback(originalRequest, originalError)
	}

	// 4. 记录降级事件
	if fh.config.LogFallbackEvents {
		fh.logFallbackEvent(originalRequest, originalError, rule, result)
	}

	return result, nil
}

// analyzeError 分析错误类型
func (fh *FallbackHandler) analyzeError(err error) string {
	errorMsg := strings.ToLower(err.Error())

	for pattern, errorPattern := range fh.errorPatterns {
		if strings.Contains(errorMsg, pattern) {
			return errorPattern.Category
		}
	}

	return "unknown"
}

// findMatchingRule 查找匹配的降级规则
func (fh *FallbackHandler) findMatchingRule(
	request *AgentDispatchRequest,
	err error,
	result *AgentDispatchResult,
) *FallbackRule {
	errorMsg := strings.ToLower(err.Error())

	for _, rule := range fh.fallbackRules {
		if !rule.Enabled {
			continue
		}

		if fh.matchesCondition(rule.Condition, request, errorMsg, result) {
			return rule
		}
	}

	return nil
}

// matchesCondition 检查是否匹配条件
func (fh *FallbackHandler) matchesCondition(
	condition *FallbackCondition,
	request *AgentDispatchRequest,
	errorMsg string,
	result *AgentDispatchResult,
) bool {
	// 检查错误模式
	if len(condition.ErrorPatterns) > 0 {
		matched := false
		for _, pattern := range condition.ErrorPatterns {
			if strings.Contains(errorMsg, strings.ToLower(pattern)) {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	// 检查置信度
	if result != nil && condition.ConfidenceBelow > 0 {
		if result.Confidence >= condition.ConfidenceBelow {
			return false
		}
	}

	// 检查Agent ID
	if len(condition.AgentIDs) > 0 && result != nil {
		matched := false
		for _, selectedAgent := range result.SelectedAgents {
			for _, agentID := range condition.AgentIDs {
				if selectedAgent.AgentID == agentID {
					matched = true
					break
				}
			}
			if matched {
				break
			}
		}
		if !matched {
			return false
		}
	}

	return true
}

// executeFallbackAction 执行降级动作
func (fh *FallbackHandler) executeFallbackAction(
	ctx context.Context,
	request *AgentDispatchRequest,
	rule *FallbackRule,
	errorCategory string,
) (*FallbackResult, error) {
	action := rule.Action

	switch action.Type {
	case "alternative_agent":
		return fh.executeAlternativeAgent(ctx, request, action)
	case "simplified_task":
		return fh.executeSimplifiedTask(ctx, request, action)
	case "manual_response":
		return fh.executeManualResponse(request, action)
	case "retry_with_config":
		return fh.executeRetryWithConfig(ctx, request, action)
	default:
		return fh.createDefaultFallback(request, fmt.Errorf("unknown fallback action: %s", action.Type))
	}
}

// executeAlternativeAgent 执行替代Agent
func (fh *FallbackHandler) executeAlternativeAgent(
	ctx context.Context,
	request *AgentDispatchRequest,
	action *FallbackAction,
) (*FallbackResult, error) {
	if action.AlternativeAgent == "" {
		return nil, fmt.Errorf("alternative agent not specified")
	}

	// 检查替代Agent是否可用
	if !fh.agentRegistry.IsAgentRegistered(action.AlternativeAgent) {
		return nil, fmt.Errorf("alternative agent not available: %s", action.AlternativeAgent)
	}

	// 创建简化的调度结果
	result := &AgentDispatchResult{
		SelectedAgents: []*SelectedAgent{
			{
				AgentID:     action.AlternativeAgent,
				AgentName:   action.AlternativeAgent,
				Capability:  "fallback_operation",
				Parameters:  make(map[string]interface{}),
				Priority:    1,
				Confidence:  0.8,
				Description: "降级处理：使用替代Agent",
			},
		},
		ExecutionPlan: &ExecutionPlan{
			Strategy: "sequential",
			Timeout:  fh.config.FallbackTimeout,
		},
		Confidence:    0.8,
		Reasoning:     "降级处理：使用替代Agent执行任务",
		EstimatedTime: 30 * time.Second,
	}

	return &FallbackResult{
		Success:        true,
		FallbackType:   "alternative_agent",
		FallbackAction: fmt.Sprintf("使用替代Agent: %s", action.AlternativeAgent),
		Result:         result,
		Message:        "已切换到替代Agent执行任务",
		Suggestions:    []string{"任务将使用备用方案执行", "可能需要更长时间完成"},
	}, nil
}

// executeSimplifiedTask 执行简化任务
func (fh *FallbackHandler) executeSimplifiedTask(
	ctx context.Context,
	request *AgentDispatchRequest,
	action *FallbackAction,
) (*FallbackResult, error) {
	if action.SimplifiedTask == nil {
		return nil, fmt.Errorf("simplified task not specified")
	}

	task := action.SimplifiedTask

	// 检查Agent是否可用
	if !fh.agentRegistry.IsAgentRegistered(task.AgentID) {
		return nil, fmt.Errorf("simplified task agent not available: %s", task.AgentID)
	}

	result := &AgentDispatchResult{
		SelectedAgents: []*SelectedAgent{
			{
				AgentID:     task.AgentID,
				AgentName:   task.AgentID,
				Capability:  task.Capability,
				Parameters:  task.Parameters,
				Priority:    1,
				Confidence:  0.7,
				Description: task.Description,
			},
		},
		ExecutionPlan: &ExecutionPlan{
			Strategy: "sequential",
			Timeout:  fh.config.FallbackTimeout,
		},
		Confidence:    0.7,
		Reasoning:     "降级处理：执行简化任务",
		EstimatedTime: 20 * time.Second,
	}

	return &FallbackResult{
		Success:        true,
		FallbackType:   "simplified_task",
		FallbackAction: "执行简化版本的任务",
		Result:         result,
		Message:        "已简化任务复杂度，使用基础功能执行",
		Suggestions:    []string{"功能可能有所限制", "建议稍后重试完整功能"},
	}, nil
}

// executeManualResponse 执行手动响应
func (fh *FallbackHandler) executeManualResponse(
	request *AgentDispatchRequest,
	action *FallbackAction,
) (*FallbackResult, error) {
	return &FallbackResult{
		Success:        false,
		FallbackType:   "manual_response",
		FallbackAction: "提供手动响应",
		Message:        action.ManualResponse,
		Suggestions: []string{
			"请检查输入是否正确",
			"稍后重试或联系管理员",
			"查看帮助文档获取更多信息",
		},
	}, nil
}

// executeRetryWithConfig 执行重试配置
func (fh *FallbackHandler) executeRetryWithConfig(
	ctx context.Context,
	request *AgentDispatchRequest,
	action *FallbackAction,
) (*FallbackResult, error) {
	return &FallbackResult{
		Success:        false,
		FallbackType:   "retry_config",
		FallbackAction: "配置重试参数",
		Message:        "系统将使用优化的参数重试执行",
		Suggestions: []string{
			"正在重新配置执行参数",
			"请稍等片刻",
		},
		Metadata: map[string]interface{}{
			"retry_config": action.RetryConfig,
		},
	}, nil
}

// createDefaultFallback 创建默认降级
func (fh *FallbackHandler) createDefaultFallback(
	request *AgentDispatchRequest,
	originalError error,
) (*FallbackResult, error) {
	return &FallbackResult{
		Success:       false,
		FallbackType:  "default",
		OriginalError: originalError.Error(),
		Message:       "抱歉，当前无法处理您的请求。系统正在尝试其他方案。",
		Suggestions: []string{
			"请检查输入格式是否正确",
			"稍后重试或使用更简单的表达方式",
			"联系管理员获取帮助",
		},
	}, nil
}

// initializeDefaultRules 初始化默认规则
func (fh *FallbackHandler) initializeDefaultRules() {
	// DeepSeek API失败降级规则
	fh.fallbackRules = append(fh.fallbackRules, &FallbackRule{
		ID:   "deepseek_api_failure",
		Name: "DeepSeek API失败降级",
		Condition: &FallbackCondition{
			ErrorPatterns: []string{"deepseek", "api", "timeout", "connection"},
		},
		Action: &FallbackAction{
			Type:           "manual_response",
			ManualResponse: "AI服务暂时不可用，请稍后重试或使用基础命令。",
		},
		Priority: 1,
		Enabled:  true,
	})

	// Agent不可用降级规则
	fh.fallbackRules = append(fh.fallbackRules, &FallbackRule{
		ID:   "agent_unavailable",
		Name: "Agent不可用降级",
		Condition: &FallbackCondition{
			ErrorPatterns: []string{"agent not found", "agent unavailable"},
		},
		Action: &FallbackAction{
			Type: "simplified_task",
			SimplifiedTask: &SimplifiedTask{
				AgentID:     "host_management_agent",
				Capability:  "basic_operation",
				Parameters:  make(map[string]interface{}),
				Description: "使用基础主机管理功能",
			},
		},
		Priority: 2,
		Enabled:  true,
	})

	// 低置信度降级规则
	fh.fallbackRules = append(fh.fallbackRules, &FallbackRule{
		ID:   "low_confidence",
		Name: "低置信度降级",
		Condition: &FallbackCondition{
			ConfidenceBelow: 0.5,
		},
		Action: &FallbackAction{
			Type:           "manual_response",
			ManualResponse: "我不太确定如何处理您的请求，请提供更详细的信息或使用更明确的表达。",
		},
		Priority: 3,
		Enabled:  true,
	})
}

// initializeErrorPatterns 初始化错误模式
func (fh *FallbackHandler) initializeErrorPatterns() {
	fh.errorPatterns["timeout"] = &ErrorPattern{
		Pattern:     "timeout",
		Category:    "network",
		Severity:    "medium",
		Description: "网络超时错误",
		Solution:    "检查网络连接，稍后重试",
	}

	fh.errorPatterns["connection"] = &ErrorPattern{
		Pattern:     "connection",
		Category:    "network",
		Severity:    "high",
		Description: "连接错误",
		Solution:    "检查服务状态和网络配置",
	}

	fh.errorPatterns["permission"] = &ErrorPattern{
		Pattern:     "permission",
		Category:    "security",
		Severity:    "high",
		Description: "权限错误",
		Solution:    "检查用户权限和访问控制",
	}

	fh.errorPatterns["not found"] = &ErrorPattern{
		Pattern:     "not found",
		Category:    "resource",
		Severity:    "medium",
		Description: "资源未找到",
		Solution:    "确认资源是否存在，检查路径或ID",
	}
}

// logFallbackEvent 记录降级事件
func (fh *FallbackHandler) logFallbackEvent(
	request *AgentDispatchRequest,
	originalError error,
	rule *FallbackRule,
	result *FallbackResult,
) {
	fh.logger.WithFields(logrus.Fields{
		"session_id":      request.SessionID,
		"user_message":    request.UserMessage,
		"original_error":  originalError.Error(),
		"fallback_rule":   rule.ID,
		"fallback_type":   result.FallbackType,
		"fallback_success": result.Success,
	}).Info("Fallback event logged")
}
