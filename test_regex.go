package main

import (
	"fmt"
	"regexp"
)

func main() {
	input := "列出主机"

	listPatterns := []string{
		"查看.*主机", "主机.*列表", "显示.*主机", "现有主机", "主机.*状态",
		"查询.*主机", "主机.*信息", "主机.*账号", "列出主机", "主机列表",
		"所有主机", "全部主机", "主机清单", "主机概览",
	}

	fmt.Printf("测试输入: %s\n", input)

	for _, pattern := range listPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			fmt.Printf("✅ 匹配成功: %s\n", pattern)
			return
		} else {
			fmt.Printf("❌ 不匹配: %s\n", pattern)
		}
	}

	fmt.Println("没有匹配的模式")
}
