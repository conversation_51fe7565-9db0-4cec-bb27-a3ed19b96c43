package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"strings"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"
	"aiops-platform/internal/security"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 工作流相关类型定义
type WorkflowIntentAnalysis struct {
	NeedsWorkflow   bool                   `json:"needs_workflow"`
	WorkflowType    string                 `json:"workflow_type"`
	Confidence      float64                `json:"confidence"`
	Intent          string                 `json:"intent"`
	SuggestedAction string                 `json:"suggested_action"`
	Parameters      map[string]interface{} `json:"parameters"`
	Context         string                 `json:"context"`
}

type WorkflowState struct {
	InstanceID    string                 `json:"instance_id"`
	DefinitionID  string                 `json:"definition_id"`
	CurrentStep   string                 `json:"current_step"`
	Status        string                 `json:"status"`
	Variables     map[string]interface{} `json:"variables"`
	CollectedData map[string]interface{} `json:"collected_data"`
	Progress      float64                `json:"progress"`
}

type WorkflowGuidance struct {
	Message           string   `json:"message"`
	Suggestions       []string `json:"suggestions"`
	NextAction        string   `json:"next_action"`
	RequiredInputs    []string `json:"required_inputs"`
	ProgressIndicator string   `json:"progress_indicator"`
	HelpText          string   `json:"help_text"`
}

// AIService 增强的AI服务接口
type AIService interface {
	// 核心对话功能
	ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error)
	ProcessMessageWithTools(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error)

	// 上下文管理
	CreateContext(sessionID string, userID int64) error
	GetContext(sessionID string) (*ConversationContext, error)
	UpdateContext(sessionID string, updates map[string]interface{}) error
	ClearContext(sessionID string) error

	// 意图和工具
	ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error)
	GetAvailableTools(userID int64) ([]ToolDefinition, error)
	ExecuteTool(ctx context.Context, toolCall *ToolCall, context *ConversationContext) (*ToolResult, error)

	// 高级功能
	GenerateResponse(ctx context.Context, req *GenerateResponseRequest) (*GenerateResponseResult, error)
	SummarizeConversation(ctx context.Context, sessionID string) (*ConversationSummary, error)
	ValidateCommand(ctx context.Context, command string, context *ConversationContext) (*CommandValidation, error)

	// 工作流集成功能
	ProcessMessageWithWorkflow(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error)
	AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (*WorkflowIntentAnalysis, error)
	GenerateWorkflowGuidance(ctx context.Context, workflowState *WorkflowState) (*WorkflowGuidance, error)
}

// aiService AI服务实现
type aiService struct {
	db                       *gorm.DB
	config                   *config.Config
	logger                   *logrus.Logger
	deepseekService          *DeepSeekService
	mockAIService            *MockAIService
	toolManager              *ToolManager
	contextManager           *ContextManager
	enhancedContextManager   *EnhancedContextManager
	smartConversationManager *SmartConversationManager
	intentRecognizer         *IntentRecognizer
	intentContinuityManager  *IntentContinuityManager
	commandGenerator         *CommandGenerator
	actionRecognizer         *ActionRecognizer
	hostService              HostService
	useMockAI                bool // 是否使用模拟AI服务
	// Agent平台集成
	agentPlatformEnabled bool
	agentPlatform        interface{} // 使用interface{}避免循环导入
}

// NewAIService 创建AI服务
func NewAIService(
	db *gorm.DB,
	cfg *config.Config,
	logger *logrus.Logger,
	hostService HostService,
) AIService {
	// 创建DeepSeek服务
	deepseekService := NewDeepSeekService(&cfg.DeepSeek, logger)

	// 创建组件
	toolManager := NewToolManagerWithDB(logger, hostService, db)
	contextManager := NewContextManager(db, logger)
	intentRecognizer := NewIntentRecognizer(deepseekService, logger)
	commandGenerator := NewCommandGenerator(deepseekService, logger)

	// 创建增强组件
	enhancedContextManager := NewEnhancedContextManager(db, logger, intentRecognizer)
	intentContinuityManager := NewIntentContinuityManager(logger, intentRecognizer, enhancedContextManager)
	smartConversationManager := NewSmartConversationManager(logger, hostService)
	mockAIService := NewMockAIService(logger)
	actionRecognizer := NewActionRecognizer(logger, hostService)

	// 检查是否应该使用模拟AI服务
	useMockAI := cfg.DeepSeek.APIKey == "" || cfg.DeepSeek.APIKey == "***********************************"

	if useMockAI {
		logger.Warn("Using Mock AI Service - DeepSeek API key not configured or using placeholder")
	}

	// 创建AI服务实例
	service := &aiService{
		db:                       db,
		config:                   cfg,
		logger:                   logger,
		deepseekService:          deepseekService,
		mockAIService:            mockAIService,
		toolManager:              toolManager,
		contextManager:           contextManager,
		enhancedContextManager:   enhancedContextManager,
		smartConversationManager: smartConversationManager,
		intentRecognizer:         intentRecognizer,
		intentContinuityManager:  intentContinuityManager,
		commandGenerator:         commandGenerator,
		actionRecognizer:         actionRecognizer,
		hostService:              hostService,
		useMockAI:                useMockAI,
		// Agent平台暂时禁用，避免循环导入
		agentPlatformEnabled: false,
		agentPlatform:        nil,
	}

	return service
}

// ProcessMessage 处理消息（智能对话版本）
func (s *aiService) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	s.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("Processing message with smart conversation manager")

	// 提取意图
	intent, err := s.ExtractIntent(ctx, req.Message, nil)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to extract intent, using general chat")
		intent = &IntentResult{
			Type:       "general_chat",
			Confidence: 0.5,
			Parameters: make(map[string]interface{}),
		}
	}

	// 检查是否需要智能执行
	if s.shouldAutoExecute(intent) {
		return s.processWithSmartExecution(ctx, req, intent, start)
	}

	// 使用智能对话管理器处理消息
	smartResponse, err := s.smartConversationManager.ProcessMessage(ctx, req.SessionID, req.UserID, req.Message, intent)
	if err != nil {
		s.logger.WithError(err).Error("Smart conversation manager failed, falling back to basic processing")
		return s.processMessageBasic(ctx, req, intent, start)
	}

	// 如果需要执行操作，从SmartConversationManager获取PendingAction
	if smartResponse.Action == "execute_pending_action" || smartResponse.Action == "execute_host_list" {
		// 从SmartConversationManager获取会话的PendingAction
		pendingAction := s.smartConversationManager.GetSessionPendingAction(req.SessionID)
		if pendingAction != nil {
			executionResult, err := s.executePendingAction(ctx, pendingAction, req.UserID)
			if err != nil {
				s.logger.WithError(err).Error("Failed to execute pending action")
				smartResponse.Content = fmt.Sprintf("执行操作失败：%s", err.Error())
			} else {
				smartResponse.Content = executionResult
			}
		} else {
			s.logger.WithFields(logrus.Fields{
				"session_id": req.SessionID,
				"action":     smartResponse.Action,
			}).Warn("No pending action found for execution")
			smartResponse.Content = "没有找到待执行的操作。"
		}
	}

	// 转换为标准响应格式
	processingTime := time.Since(start)
	return &ProcessMessageResponse{
		Content:        smartResponse.Content,
		Intent:         intent.Type,
		Confidence:     intent.Confidence,
		Parameters:     intent.Parameters,
		TokenCount:     50, // 估算
		ProcessingTime: processingTime,
		Timestamp:      time.Now(),
	}, nil
}

// executePendingAction 执行待处理的操作
func (s *aiService) executePendingAction(ctx context.Context, action *SmartPendingAction, userID int64) (string, error) {
	switch action.Type {
	case "add_host":
		// 转换为IntentResult格式
		intent := &IntentResult{
			Type:       action.Intent,
			Confidence: action.Confidence,
			Parameters: action.Parameters,
		}

		response, err := s.handleHostAddition(ctx, intent, userID)
		if err != nil {
			return "", err
		}

		return response.Content, nil
	case "list_hosts":
		// 转换为IntentResult格式
		intent := &IntentResult{
			Type:       action.Intent,
			Confidence: action.Confidence,
			Parameters: action.Parameters,
		}

		response, err := s.handleHostList(ctx, intent, userID)
		if err != nil {
			return "", err
		}

		return response.Content, nil
	case "execute_command":
		// 处理命令执行
		response, err := s.handleCommandExecutionFromAction(ctx, action, userID)
		if err != nil {
			return "", err
		}
		return response, nil
	default:
		return "", fmt.Errorf("unsupported action type: %s", action.Type)
	}
}

// processMessageBasic 基础消息处理（回退方案）
func (s *aiService) processMessageBasic(ctx context.Context, req *ProcessMessageRequest, intent *IntentResult, start time.Time) (*ProcessMessageResponse, error) {
	// 获取或创建上下文
	context, err := s.getOrCreateContext(req.SessionID, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get context: %w", err)
	}

	// 特殊处理各种运维操作意图
	switch intent.Type {
	case "host_management":
		if action, ok := intent.Parameters["action"].(string); ok {
			switch action {
			case "add_host":
				response, err := s.handleHostAddition(ctx, intent, req.UserID)
				if err != nil {
					s.logger.WithError(err).Error("Failed to handle host addition")
					// 回退到普通响应
				} else {
					return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
						"action":     "add_host",
						"host_added": response.HostAdded,
					})
				}
			case "list":
				response, err := s.handleHostList(ctx, intent, req.UserID)
				if err != nil {
					s.logger.WithError(err).Error("Failed to handle host list")
					// 回退到普通响应
				} else {
					return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
						"action":     "list_hosts",
						"host_count": response.HostCount,
					})
				}
			}
		}
	case "system_monitoring":
		response, err := s.handleSystemMonitoring(ctx, intent, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to handle system monitoring")
			// 回退到普通响应
		} else {
			return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
				"action":      response.Action,
				"metric_type": response.MetricType,
				"host_count":  response.HostCount,
			})
		}
	case "service_management":
		response, err := s.handleServiceManagement(ctx, intent, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to handle service management")
			// 回退到普通响应
		} else {
			return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
				"action":  response.Action,
				"service": response.Service,
				"success": response.Success,
			})
		}
	case "log_analysis":
		response, err := s.handleLogAnalysis(ctx, intent, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to handle log analysis")
			// 回退到普通响应
		} else {
			return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
				"action":     response.Action,
				"log_type":   response.LogType,
				"line_count": response.LineCount,
			})
		}
	case "command_execution":
		response, err := s.handleCommandExecution(ctx, intent, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to handle command execution")
			// 回退到普通响应
		} else {
			return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
				"action":    response.Action,
				"command":   response.Command,
				"exit_code": response.ExitCode,
			})
		}
	case "alert_management":
		response, err := s.handleAlertManagement(ctx, intent, req.UserID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to handle alert management")
			// 回退到普通响应
		} else {
			return s.createSuccessResponse(req, intent, response.Content, start, map[string]interface{}{
				"action":      response.Action,
				"alert_count": response.AlertCount,
			})
		}
	}

	// 生成响应
	response, err := s.generateBasicResponse(ctx, req.Message, intent, context)
	if err != nil {
		return nil, fmt.Errorf("failed to generate response: %w", err)
	}

	// 更新上下文
	s.updateContextWithMessage(req.SessionID, "user", req.Message, intent.Parameters)
	s.updateContextWithMessage(req.SessionID, "assistant", response.Content, map[string]interface{}{
		"intent":      intent.Type,
		"confidence":  intent.Confidence,
		"token_count": response.TokenCount,
	})

	processingTime := time.Since(start)
	s.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          intent.Type,
		"processing_time": processingTime.Milliseconds(),
		"token_count":     response.TokenCount,
	}).Info("Message processed successfully")

	return &ProcessMessageResponse{
		Content:        response.Content,
		Intent:         intent.Type,
		Confidence:     intent.Confidence,
		Parameters:     intent.Parameters,
		TokenCount:     response.TokenCount,
		ProcessingTime: processingTime,
		Timestamp:      time.Now(),
	}, nil
}

// ProcessMessageWithTools 处理消息（工具增强版本）
func (s *aiService) ProcessMessageWithTools(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	s.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
		"with_tools": true,
	}).Info("Processing message with tools")

	// 获取或创建上下文
	context, err := s.getOrCreateContext(req.SessionID, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get context: %w", err)
	}

	// 构建系统提示
	systemPrompt := s.buildSystemPrompt(req.UserID)

	// 获取可用工具
	tools := s.toolManager.GetAvailableTools()

	// 构建消息历史
	messages, err := s.buildMessageHistory(req.SessionID, req.Message, systemPrompt)
	if err != nil {
		return nil, fmt.Errorf("failed to build message history: %w", err)
	}

	// 发送带工具的请求
	response, err := s.deepseekService.ChatWithTools(ctx, messages, tools)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI response: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	choice := response.Choices[0]
	result := &ProcessMessageResponse{
		Content:        choice.Message.Content,
		TokenCount:     response.Usage.TotalTokens,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}

	// 处理工具调用
	if len(choice.Message.ToolCalls) > 0 {
		toolResults, err := s.executeToolCalls(ctx, choice.Message.ToolCalls, context)
		if err != nil {
			s.logger.WithError(err).Error("Failed to execute tool calls")
			// 继续处理，但记录错误
		} else {
			result.ToolCalls = s.convertToolCalls(choice.Message.ToolCalls)
			result.ToolResults = toolResults

			// 如果有工具调用，可能需要再次调用AI来生成最终响应
			if len(toolResults) > 0 {
				finalResponse, err := s.generateFinalResponse(ctx, messages, choice.Message.ToolCalls, toolResults)
				if err != nil {
					s.logger.WithError(err).Warn("Failed to generate final response")
				} else {
					result.Content = finalResponse
				}
			}
		}
	}

	// 提取意图（基于最终内容）
	intent, err := s.ExtractIntent(ctx, req.Message, context)
	if err == nil {
		result.Intent = intent.Type
		result.Confidence = intent.Confidence
		result.Parameters = intent.Parameters
	}

	// 识别可执行操作
	if s.actionRecognizer != nil {
		actionResult, err := s.actionRecognizer.RecognizeActions(ctx, result.Content)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to recognize actions")
		} else {
			result.RecognizedActions = actionResult.Actions
			result.ActionSuggestions = actionResult.Suggestions
		}
	}

	// 更新上下文
	s.updateContextWithMessage(req.SessionID, "user", req.Message, result.Parameters)
	s.updateContextWithMessage(req.SessionID, "assistant", result.Content, map[string]interface{}{
		"intent":       result.Intent,
		"confidence":   result.Confidence,
		"token_count":  result.TokenCount,
		"tool_calls":   len(result.ToolCalls),
		"tool_results": len(result.ToolResults),
	})

	s.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          result.Intent,
		"processing_time": result.ProcessingTime.Milliseconds(),
		"token_count":     result.TokenCount,
		"tool_calls":      len(result.ToolCalls),
	}).Info("Message with tools processed successfully")

	return result, nil
}

// ProcessMessageWithEnhancedContext 使用增强上下文处理消息
func (s *aiService) ProcessMessageWithEnhancedContext(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	s.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
		"enhanced":   true,
	}).Info("Processing message with enhanced context")

	// 获取或创建增强上下文
	enhancedCtx, err := s.enhancedContextManager.GetEnhancedContext(req.SessionID)
	if err != nil {
		// 如果增强上下文不存在，先创建基础上下文
		if err := s.CreateContext(req.SessionID, req.UserID); err != nil {
			return nil, fmt.Errorf("failed to create context: %w", err)
		}

		enhancedCtx, err = s.enhancedContextManager.GetEnhancedContext(req.SessionID)
		if err != nil {
			return nil, fmt.Errorf("failed to get enhanced context: %w", err)
		}
	}

	// 处理意图连续性
	continuityResult, err := s.intentContinuityManager.ProcessIntentContinuity(ctx, req.SessionID, req.Message)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to process intent continuity")
	}

	// 创建增强消息
	userMessage := &EnhancedContextMessage{
		ID:        fmt.Sprintf("user_%d", time.Now().UnixNano()),
		Role:      "user",
		Content:   req.Message,
		Timestamp: time.Now(),
	}

	// 添加到增强上下文
	if err := s.enhancedContextManager.AddEnhancedMessage(req.SessionID, userMessage); err != nil {
		s.logger.WithError(err).Error("Failed to add user message to enhanced context")
	}

	// 构建系统提示（包含连续性信息）
	systemPrompt := s.buildEnhancedSystemPrompt(req.UserID, continuityResult)

	// 获取可用工具
	tools := s.toolManager.GetAvailableTools()

	// 构建消息历史（使用增强上下文）
	messages, err := s.buildEnhancedMessageHistory(req.SessionID, req.Message, systemPrompt, enhancedCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to build enhanced message history: %w", err)
	}

	// 发送带工具的请求
	response, err := s.deepseekService.ChatWithTools(ctx, messages, tools)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI response: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	choice := response.Choices[0]
	result := &ProcessMessageResponse{
		Content:        choice.Message.Content,
		TokenCount:     response.Usage.TotalTokens,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}

	// 添加连续性信息
	if continuityResult != nil {
		result.Intent = continuityResult.CurrentIntent
		result.Confidence = continuityResult.Confidence
		result.Parameters = continuityResult.ContextVariables
	}

	// 处理工具调用
	if len(choice.Message.ToolCalls) > 0 {
		toolResults, err := s.executeToolCalls(ctx, choice.Message.ToolCalls, &ConversationContext{
			SessionID: req.SessionID,
			UserID:    req.UserID,
		})
		if err != nil {
			s.logger.WithError(err).Error("Failed to execute tool calls")
		} else {
			result.ToolCalls = s.convertToolCalls(choice.Message.ToolCalls)
			result.ToolResults = toolResults

			// 如果有工具调用，可能需要再次调用AI来生成最终响应
			if len(toolResults) > 0 {
				finalResponse, err := s.generateFinalResponse(ctx, messages, choice.Message.ToolCalls, toolResults)
				if err != nil {
					s.logger.WithError(err).Warn("Failed to generate final response")
				} else {
					result.Content = finalResponse
				}
			}
		}
	}

	// 识别可执行操作
	if s.actionRecognizer != nil {
		actionResult, err := s.actionRecognizer.RecognizeActions(ctx, result.Content)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to recognize actions")
		} else {
			result.RecognizedActions = actionResult.Actions
			result.ActionSuggestions = actionResult.Suggestions
		}
	}

	// 创建助手消息并添加到增强上下文
	assistantMessage := &EnhancedContextMessage{
		ID:         fmt.Sprintf("assistant_%d", time.Now().UnixNano()),
		Role:       "assistant",
		Content:    result.Content,
		Intent:     result.Intent,
		Confidence: result.Confidence,
		ToolCalls:  s.extractToolCallNames(choice.Message.ToolCalls),
		Timestamp:  time.Now(),
		Metadata: map[string]interface{}{
			"token_count":  result.TokenCount,
			"tool_calls":   len(result.ToolCalls),
			"tool_results": len(result.ToolResults),
		},
	}

	if err := s.enhancedContextManager.AddEnhancedMessage(req.SessionID, assistantMessage); err != nil {
		s.logger.WithError(err).Error("Failed to add assistant message to enhanced context")
	}

	s.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          result.Intent,
		"processing_time": result.ProcessingTime.Milliseconds(),
		"token_count":     result.TokenCount,
		"tool_calls":      len(result.ToolCalls),
		"continuity_score": func() float64 {
			if continuityResult != nil {
				return continuityResult.ContinuityScore
			}
			return 0.0
		}(),
	}).Info("Enhanced message processed successfully")

	return result, nil
}

// CreateContext 创建新的对话上下文
func (s *aiService) CreateContext(sessionID string, userID int64) error {
	// 创建新的会话记录
	session := &model.ChatSession{
		SessionID:    sessionID,
		UserID:       userID,
		Status:       "active",
		StartedAt:    time.Now(),
		LastActivity: time.Now(),
		ContextData:  "{}",
	}

	if err := s.db.Create(session).Error; err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	// 创建内存中的上下文
	ctx := &SessionContext{
		SessionID:    sessionID,
		UserID:       userID,
		Messages:     []ContextMessage{},
		Variables:    make(map[string]interface{}),
		LastActivity: time.Now(),
		MaxMessages:  50,
	}

	// 缓存上下文
	s.contextManager.mutex.Lock()
	s.contextManager.cache[sessionID] = ctx
	s.contextManager.mutex.Unlock()

	return nil
}

// GetContext 获取对话上下文
func (s *aiService) GetContext(sessionID string) (*ConversationContext, error) {
	sessionCtx, err := s.contextManager.GetContext(sessionID)
	if err != nil {
		return nil, err
	}

	return &ConversationContext{
		SessionID:    sessionCtx.SessionID,
		UserID:       sessionCtx.UserID,
		Messages:     s.convertContextMessages(sessionCtx.Messages),
		Variables:    sessionCtx.Variables,
		LastActivity: sessionCtx.LastActivity,
	}, nil
}

// UpdateContext 更新对话上下文
func (s *aiService) UpdateContext(sessionID string, updates map[string]interface{}) error {
	for key, value := range updates {
		if err := s.contextManager.SetVariable(sessionID, key, value); err != nil {
			return err
		}
	}
	return nil
}

// ClearContext 清除对话上下文
func (s *aiService) ClearContext(sessionID string) error {
	// 从缓存中删除
	s.contextManager.mutex.Lock()
	delete(s.contextManager.cache, sessionID)
	s.contextManager.mutex.Unlock()

	// 从数据库中删除
	if err := s.db.Where("session_id = ?", sessionID).Delete(&model.ChatSession{}).Error; err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}

	return nil
}

// ExtractIntent 提取用户意图
func (s *aiService) ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error) {
	if s.useMockAI {
		// 使用模拟AI服务
		mockIntent, err := s.mockAIService.RecognizeIntent(ctx, message)
		if err != nil {
			return nil, err
		}

		return &IntentResult{
			Type:       mockIntent.Type,
			Confidence: mockIntent.Confidence,
			Parameters: mockIntent.Parameters,
			Command:    "", // 模拟服务不生成命令
		}, nil
	}

	// 使用真实的DeepSeek服务
	intent, err := s.intentRecognizer.RecognizeIntent(ctx, message)
	if err != nil {
		// 如果DeepSeek服务失败，降级到模拟服务
		s.logger.WithError(err).Warn("DeepSeek service failed, falling back to mock AI")
		s.useMockAI = true

		mockIntent, mockErr := s.mockAIService.RecognizeIntent(ctx, message)
		if mockErr != nil {
			return nil, fmt.Errorf("both DeepSeek and mock AI failed: %w", err)
		}

		return &IntentResult{
			Type:       mockIntent.Type,
			Confidence: mockIntent.Confidence,
			Parameters: mockIntent.Parameters,
			Command:    "",
		}, nil
	}

	return &IntentResult{
		Type:       intent.Type,
		Confidence: intent.Confidence,
		Parameters: intent.Parameters,
		Command:    intent.Command,
	}, nil
}

// GetAvailableTools 获取可用工具
func (s *aiService) GetAvailableTools(userID int64) ([]ToolDefinition, error) {
	tools := s.toolManager.GetAvailableTools()
	result := make([]ToolDefinition, len(tools))

	for i, tool := range tools {
		result[i] = ToolDefinition{
			Name:        tool.Function.Name,
			Description: tool.Function.Description,
			Parameters:  tool.Function.Parameters,
		}
	}

	return result, nil
}

// ExecuteTool 执行工具
func (s *aiService) ExecuteTool(ctx context.Context, toolCall *ToolCall, context *ConversationContext) (*ToolResult, error) {
	// 转换为内部格式
	internalToolCall := ToolCall{
		ID:   toolCall.ID,
		Type: toolCall.Type,
		Function: struct {
			Name      string `json:"name"`
			Arguments string `json:"arguments"`
		}{
			Name:      toolCall.Function.Name,
			Arguments: toolCall.Function.Arguments,
		},
	}

	result, err := s.toolManager.ExecuteTool(ctx, internalToolCall)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 序列化结果
	resultData, _ := json.Marshal(result)

	return &ToolResult{
		Success: true,
		Data:    result,
		Raw:     string(resultData),
	}, nil
}

// GenerateResponse 生成响应
func (s *aiService) GenerateResponse(ctx context.Context, req *GenerateResponseRequest) (*GenerateResponseResult, error) {
	messages := []Message{
		{Role: "system", Content: req.SystemPrompt},
		{Role: "user", Content: req.UserMessage},
	}

	// 添加历史消息
	if req.Context != nil && len(req.Context.Messages) > 0 {
		// 插入历史消息到用户消息之前
		historyMessages := make([]Message, 0, len(req.Context.Messages))
		for _, msg := range req.Context.Messages {
			historyMessages = append(historyMessages, Message{
				Role:    msg.Role,
				Content: msg.Content,
			})
		}

		// 重新构建消息数组
		messages = []Message{
			{Role: "system", Content: req.SystemPrompt},
		}
		messages = append(messages, historyMessages...)
		messages = append(messages, Message{Role: "user", Content: req.UserMessage})
	}

	var response *ChatResponse
	var err error

	if len(req.Tools) > 0 {
		response, err = s.deepseekService.ChatWithTools(ctx, messages, req.Tools)
	} else {
		response, err = s.deepseekService.Chat(ctx, messages)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to generate response: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	choice := response.Choices[0]
	return &GenerateResponseResult{
		Content:      choice.Message.Content,
		ToolCalls:    s.convertToolCalls(choice.Message.ToolCalls),
		TokenCount:   response.Usage.TotalTokens,
		FinishReason: choice.FinishReason,
	}, nil
}

// SummarizeConversation 总结对话
func (s *aiService) SummarizeConversation(ctx context.Context, sessionID string) (*ConversationSummary, error) {
	context, err := s.contextManager.GetContext(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get context: %w", err)
	}

	if len(context.Messages) == 0 {
		return &ConversationSummary{
			SessionID: sessionID,
			Summary:   "暂无对话内容",
			KeyPoints: []string{},
		}, nil
	}

	// 构建对话历史
	var conversationText strings.Builder
	for _, msg := range context.Messages {
		conversationText.WriteString(fmt.Sprintf("%s: %s\n", msg.Role, msg.Content))
	}

	systemPrompt := `你是一个专业的对话总结助手。请分析以下运维对话内容，生成简洁的总结。

请返回JSON格式，包含：
- summary: 对话的简要总结（1-2句话）
- key_points: 关键要点列表（最多5个）
- actions_taken: 已执行的操作列表
- next_steps: 建议的后续步骤

示例：
{
  "summary": "用户询问了服务器状态并执行了系统检查",
  "key_points": ["检查了CPU使用率", "查看了内存状态", "测试了网络连接"],
  "actions_taken": ["执行了top命令", "运行了free -h"],
  "next_steps": ["建议定期监控", "考虑优化配置"]
}`

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: conversationText.String()},
	}

	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to generate summary: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	content := response.Choices[0].Message.Content

	// 尝试解析JSON
	var summaryData struct {
		Summary      string   `json:"summary"`
		KeyPoints    []string `json:"key_points"`
		ActionsTaken []string `json:"actions_taken"`
		NextSteps    []string `json:"next_steps"`
	}

	if err := json.Unmarshal([]byte(content), &summaryData); err != nil {
		// 如果解析失败，返回基础总结
		return &ConversationSummary{
			SessionID: sessionID,
			Summary:   content,
			KeyPoints: []string{},
		}, nil
	}

	return &ConversationSummary{
		SessionID:    sessionID,
		Summary:      summaryData.Summary,
		KeyPoints:    summaryData.KeyPoints,
		ActionsTaken: summaryData.ActionsTaken,
		NextSteps:    summaryData.NextSteps,
		GeneratedAt:  time.Now(),
	}, nil
}

// ValidateCommand 验证命令
func (s *aiService) ValidateCommand(ctx context.Context, command string, context *ConversationContext) (*CommandValidation, error) {
	systemPrompt := `你是一个Linux命令安全专家。请分析给定的命令，评估其安全性和风险等级。

请返回JSON格式，包含：
- is_safe: 命令是否安全（true/false）
- risk_level: 风险等级（low/medium/high/critical）
- risks: 潜在风险列表
- suggestions: 安全建议
- alternative: 更安全的替代命令（如果有）

风险等级定义：
- low: 只读操作，无风险
- medium: 可能影响性能或产生大量输出
- high: 可能修改系统配置或文件
- critical: 可能导致系统损坏或数据丢失

示例：
{
  "is_safe": false,
  "risk_level": "critical",
  "risks": ["可能删除重要文件", "不可逆操作"],
  "suggestions": ["使用ls命令先查看", "添加-i参数确认"],
  "alternative": "ls -la /path/to/check"
}`

	prompt := fmt.Sprintf("请分析以下命令的安全性：%s", command)

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: prompt},
	}

	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to validate command: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	content := response.Choices[0].Message.Content

	// 尝试解析JSON
	var validation struct {
		IsSafe      bool     `json:"is_safe"`
		RiskLevel   string   `json:"risk_level"`
		Risks       []string `json:"risks"`
		Suggestions []string `json:"suggestions"`
		Alternative string   `json:"alternative"`
	}

	if err := json.Unmarshal([]byte(content), &validation); err != nil {
		// 如果解析失败，返回保守的验证结果
		return &CommandValidation{
			Command:     command,
			IsSafe:      false,
			RiskLevel:   "high",
			Risks:       []string{"无法解析安全性分析"},
			Suggestions: []string{"请手动检查命令安全性"},
		}, nil
	}

	return &CommandValidation{
		Command:     command,
		IsSafe:      validation.IsSafe,
		RiskLevel:   validation.RiskLevel,
		Risks:       validation.Risks,
		Suggestions: validation.Suggestions,
		Alternative: validation.Alternative,
		ValidatedAt: time.Now(),
	}, nil
}

// 辅助方法

// getOrCreateContext 获取或创建上下文
func (s *aiService) getOrCreateContext(sessionID string, userID int64) (*ConversationContext, error) {
	context, err := s.GetContext(sessionID)
	if err != nil {
		// 如果上下文不存在，创建新的
		if err := s.CreateContext(sessionID, userID); err != nil {
			return nil, fmt.Errorf("failed to create context: %w", err)
		}

		return &ConversationContext{
			SessionID:    sessionID,
			UserID:       userID,
			Messages:     []ConversationMessage{},
			Variables:    make(map[string]interface{}),
			LastActivity: time.Now(),
		}, nil
	}

	return context, nil
}

// generateBasicResponse 生成基础响应
func (s *aiService) generateBasicResponse(ctx context.Context, message string, intent *IntentResult, context *ConversationContext) (*GenerateResponseResult, error) {
	systemPrompt := s.buildSystemPrompt(context.UserID)

	req := &GenerateResponseRequest{
		SystemPrompt: systemPrompt,
		UserMessage:  message,
		Context:      context,
	}

	return s.GenerateResponse(ctx, req)
}

// buildSystemPrompt 构建系统提示
func (s *aiService) buildSystemPrompt(userID int64) string {
	// 获取实时系统数据上下文
	systemContext := s.buildSystemDataContext()

	return `你是一个专业的AI运维助手，名叫AI运维助手。你具备以下能力：

1. **主机管理**：查看、添加、删除主机，测试连接状态
   - 当用户说"添加主机"、"添加主机账号"、"注册新主机"、"创建主机"时，使用create_host工具
   - 当用户说"查看主机"、"主机列表"、"查询现有主机账号"、"查询主机账号"、"显示主机账号信息"、"查询主机信息"、"现有主机"、"主机账号信息"时，使用list_hosts工具
   - 当用户说"测试连接"时，使用test_host_connection工具

2. **数据库操作**：通过自然语言进行数据库管理
   - 当用户说"删除主机X"、"移除主机"时，使用database_operation工具进行delete操作
   - 当用户说"修改主机X的IP"、"更新主机信息"时，使用database_operation工具进行update操作
   - 当用户说"查看表结构"、"显示字段"时，使用database_operation工具进行describe操作
   - 支持的表：hosts(主机), users(用户), alerts(告警), operation_logs(操作日志)
   - 注意：主机查询优先使用list_hosts工具，而不是database_operation工具

3. **命令执行**：在指定主机上执行安全的系统命令
4. **系统监控**：检查CPU、内存、磁盘、网络使用情况
5. **日志分析**：查看和分析系统日志
6. **告警管理**：处理和响应系统告警
7. **安全检查**：评估命令安全性，提供安全建议

## 重要提示：
- 当用户提到"添加主机账号"、"添加主机"、"新增主机"等关键词时，应该立即使用create_host工具
- 如果用户没有提供完整的主机信息，应该引导用户提供必要的信息（主机名、IP地址、用户名）
- 创建主机后，建议用户测试连接

## 数据库操作安全原则：
- 所有修改操作（update、delete）都需要用户确认
- 在执行前显示将要执行的SQL语句和影响的记录数
- 危险操作（如删除）需要二次确认
- 操作完成后提供详细的结果反馈和撤销建议

## 数据库操作示例：
- "查看所有生产环境主机" → database_operation(operation_type="select", table_name="hosts", conditions={"environment": "production"})
- "删除名为test-01的主机" → database_operation(operation_type="delete", table_name="hosts", conditions={"name": "test-01"})
- "修改主机web-01的IP为*************" → database_operation(operation_type="update", table_name="hosts", conditions={"name": "web-01"}, data={"ip_address": "*************"})

## 工作原则：
- **安全第一**：执行任何操作前都要确认安全性
- **谨慎操作**：对于危险命令要明确警告并要求确认
- **详细记录**：所有操作都要记录日志
- **友好交互**：用专业但易懂的语言与用户交流
- **主动建议**：根据情况主动提供优化建议
- **数据驱动**：基于实时系统数据提供准确的分析和建议

## 响应风格：
- 使用友好、专业的语气
- 提供清晰的步骤说明
- 在必要时询问确认
- 解释操作的目的和影响
- 结合实时数据提供具体的分析

## 当前系统状态：
` + systemContext + `

请根据用户的问题和当前系统状态，选择合适的工具来帮助解决问题。如果需要执行危险操作，请先警告用户并要求确认。
在回答时，请充分利用上述系统状态信息，提供基于实际数据的准确分析和建议。`
}

// buildMessageHistory 构建消息历史
func (s *aiService) buildMessageHistory(sessionID, currentMessage, systemPrompt string) ([]Message, error) {
	messages := []Message{
		{Role: "system", Content: systemPrompt},
	}

	// 获取最近的消息历史
	recentMessages, err := s.contextManager.GetRecentMessages(sessionID, 10)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get recent messages, continuing without history")
	} else {
		messages = append(messages, recentMessages...)
	}

	// 添加当前用户消息
	messages = append(messages, Message{Role: "user", Content: currentMessage})

	return messages, nil
}

// executeToolCalls 执行工具调用
func (s *aiService) executeToolCalls(ctx context.Context, toolCalls []ToolCall, context *ConversationContext) ([]ToolResult, error) {
	results := make([]ToolResult, 0, len(toolCalls))

	for _, toolCall := range toolCalls {
		s.logger.WithFields(logrus.Fields{
			"tool_name": toolCall.Function.Name,
			"tool_id":   toolCall.ID,
			"session":   context.SessionID,
		}).Info("Executing tool call")

		result, err := s.toolManager.ExecuteTool(ctx, toolCall)
		if err != nil {
			s.logger.WithError(err).Error("Tool execution failed")
			results = append(results, ToolResult{
				Success: false,
				Error:   err.Error(),
			})
			continue
		}

		// 序列化结果
		resultData, _ := json.Marshal(result)
		results = append(results, ToolResult{
			Success: true,
			Data:    result,
			Raw:     string(resultData),
		})
	}

	return results, nil
}

// generateFinalResponse 生成最终响应
func (s *aiService) generateFinalResponse(ctx context.Context, messages []Message, toolCalls []ToolCall, toolResults []ToolResult) (string, error) {
	// 构建工具结果消息
	for i, toolCall := range toolCalls {
		if i < len(toolResults) {
			result := toolResults[i]
			resultContent := result.Raw
			if resultContent == "" {
				if result.Success {
					resultContent = "操作执行成功"
				} else {
					resultContent = fmt.Sprintf("操作执行失败: %s", result.Error)
				}
			}

			messages = append(messages, Message{
				Role:       "tool",
				Content:    resultContent,
				ToolCallID: toolCall.ID,
			})
		}
	}

	// 请求AI生成最终响应
	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return "", fmt.Errorf("failed to generate final response: %w", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response from AI")
	}

	return response.Choices[0].Message.Content, nil
}

// convertToolCalls 转换工具调用格式
func (s *aiService) convertToolCalls(toolCalls []ToolCall) []ToolCallResult {
	results := make([]ToolCallResult, len(toolCalls))
	for i, tc := range toolCalls {
		results[i] = ToolCallResult{
			ID:   tc.ID,
			Type: tc.Type,
			Function: struct {
				Name      string `json:"name"`
				Arguments string `json:"arguments"`
			}{
				Name:      tc.Function.Name,
				Arguments: tc.Function.Arguments,
			},
		}
	}
	return results
}

// convertContextMessages 转换上下文消息格式
func (s *aiService) convertContextMessages(messages []ContextMessage) []ConversationMessage {
	results := make([]ConversationMessage, len(messages))
	for i, msg := range messages {
		results[i] = ConversationMessage{
			Role:      msg.Role,
			Content:   msg.Content,
			Timestamp: msg.Timestamp,
			Metadata:  msg.Metadata,
		}
	}
	return results
}

// updateContextWithMessage 更新上下文消息
func (s *aiService) updateContextWithMessage(sessionID, role, content string, metadata map[string]interface{}) {
	if err := s.contextManager.AddMessage(sessionID, role, content, metadata); err != nil {
		s.logger.WithError(err).Error("Failed to update context with message")
	}
}

// buildEnhancedSystemPrompt 构建增强系统提示
func (s *aiService) buildEnhancedSystemPrompt(userID int64, continuityResult *IntentContinuityResult) string {
	basePrompt := s.buildSystemPrompt(userID)

	if continuityResult == nil {
		return basePrompt
	}

	enhancedPrompt := basePrompt + "\n\n## 对话连续性信息\n"

	if continuityResult.CurrentIntent != "" {
		enhancedPrompt += fmt.Sprintf("当前意图: %s (置信度: %.2f)\n", continuityResult.CurrentIntent, continuityResult.Confidence)
	}

	if continuityResult.ContinuityScore > 0 {
		enhancedPrompt += fmt.Sprintf("对话连续性评分: %.2f\n", continuityResult.ContinuityScore)
	}

	if len(continuityResult.IntentChain) > 0 {
		enhancedPrompt += "意图链: "
		for i, node := range continuityResult.IntentChain {
			if i > 0 {
				enhancedPrompt += " -> "
			}
			enhancedPrompt += fmt.Sprintf("%s(%s)", node.Intent, node.Status)
		}
		enhancedPrompt += "\n"
	}

	if len(continuityResult.PendingActions) > 0 {
		enhancedPrompt += fmt.Sprintf("待处理动作: %d个\n", len(continuityResult.PendingActions))
	}

	if continuityResult.RequiresFollowUp {
		enhancedPrompt += "需要后续跟进\n"
	}

	if len(continuityResult.NextSteps) > 0 {
		enhancedPrompt += "建议下一步: " + strings.Join(continuityResult.NextSteps, ", ") + "\n"
	}

	enhancedPrompt += "\n请根据以上上下文信息，提供更加连贯和有针对性的回复。"

	return enhancedPrompt
}

// buildEnhancedMessageHistory 构建增强消息历史
func (s *aiService) buildEnhancedMessageHistory(sessionID, currentMessage, systemPrompt string, enhancedCtx *EnhancedConversationContext) ([]Message, error) {
	messages := []Message{
		{Role: "system", Content: systemPrompt},
	}

	// 使用增强上下文的消息
	for _, msg := range enhancedCtx.Messages {
		// 跳过重要性太低的消息（除非是最近的消息）
		if msg.Importance < 3 && len(enhancedCtx.Messages) > 10 {
			continue
		}

		message := Message{
			Role:    msg.Role,
			Content: msg.Content,
		}

		// 如果有工具调用ID，添加到消息中
		if msg.Role == "tool" && len(msg.ToolCalls) > 0 {
			message.ToolCallID = msg.ToolCalls[0] // 简化处理
		}

		messages = append(messages, message)
	}

	// 添加当前用户消息
	messages = append(messages, Message{Role: "user", Content: currentMessage})

	return messages, nil
}

// extractToolCallNames 提取工具调用名称
func (s *aiService) extractToolCallNames(toolCalls []ToolCall) []string {
	names := make([]string, len(toolCalls))
	for i, tc := range toolCalls {
		names[i] = tc.Function.Name
	}
	return names
}

// buildSystemDataContext 构建系统数据上下文
func (s *aiService) buildSystemDataContext() string {
	var contextBuilder strings.Builder

	// 获取主机概览信息
	if hostSummary := s.getHostSummary(); hostSummary != "" {
		contextBuilder.WriteString("### 主机状态概览\n")
		contextBuilder.WriteString(hostSummary)
		contextBuilder.WriteString("\n\n")
	}

	// 获取告警概览信息
	if alertSummary := s.getAlertSummary(); alertSummary != "" {
		contextBuilder.WriteString("### 告警状态概览\n")
		contextBuilder.WriteString(alertSummary)
		contextBuilder.WriteString("\n\n")
	}

	// 获取系统统计信息
	if systemStats := s.getSystemStats(); systemStats != "" {
		contextBuilder.WriteString("### 系统统计信息\n")
		contextBuilder.WriteString(systemStats)
		contextBuilder.WriteString("\n\n")
	}

	if contextBuilder.Len() == 0 {
		return "暂无系统状态数据"
	}

	return contextBuilder.String()
}

// getHostSummary 获取主机概览信息
func (s *aiService) getHostSummary() string {
	// 获取主机统计信息
	var totalHosts, onlineHosts, offlineHosts int64

	// 查询总主机数
	if err := s.db.Model(&model.Host{}).Count(&totalHosts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count total hosts")
		return ""
	}

	// 查询在线主机数
	if err := s.db.Model(&model.Host{}).Where("status = ?", "online").Count(&onlineHosts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count online hosts")
		return ""
	}

	// 查询离线主机数
	if err := s.db.Model(&model.Host{}).Where("status IN ?", []string{"offline", "unknown"}).Count(&offlineHosts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count offline hosts")
		return ""
	}

	// 获取最近的主机列表（前5个）
	var recentHosts []model.Host
	if err := s.db.Model(&model.Host{}).Order("updated_at DESC").Limit(5).Find(&recentHosts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get recent hosts")
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("- 总主机数: %d台\n", totalHosts))
	summary.WriteString(fmt.Sprintf("- 在线主机: %d台\n", onlineHosts))
	summary.WriteString(fmt.Sprintf("- 离线主机: %d台\n", offlineHosts))

	if len(recentHosts) > 0 {
		summary.WriteString("- 最近更新的主机:\n")
		for _, host := range recentHosts {
			summary.WriteString(fmt.Sprintf("  * %s (%s) - %s\n", host.Name, host.IPAddress, host.Status))
		}
	}

	return summary.String()
}

// getAlertSummary 获取告警概览信息
func (s *aiService) getAlertSummary() string {
	// 获取告警统计信息
	var totalAlerts, criticalAlerts, warningAlerts, activeAlerts int64

	// 查询总告警数
	if err := s.db.Model(&model.Alert{}).Count(&totalAlerts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count total alerts")
		return ""
	}

	// 查询严重告警数
	if err := s.db.Model(&model.Alert{}).Where("level = ? AND status = ?", "critical", "active").Count(&criticalAlerts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count critical alerts")
		return ""
	}

	// 查询警告告警数
	if err := s.db.Model(&model.Alert{}).Where("level = ? AND status = ?", "warning", "active").Count(&warningAlerts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count warning alerts")
		return ""
	}

	// 查询活跃告警数
	if err := s.db.Model(&model.Alert{}).Where("status = ?", "active").Count(&activeAlerts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count active alerts")
		return ""
	}

	// 获取最近的告警（前3个）
	var recentAlerts []model.Alert
	if err := s.db.Model(&model.Alert{}).Preload("Host").Where("status = ?", "active").Order("alert_time DESC").Limit(3).Find(&recentAlerts).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get recent alerts")
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("- 总告警数: %d条\n", totalAlerts))
	summary.WriteString(fmt.Sprintf("- 活跃告警: %d条\n", activeAlerts))
	summary.WriteString(fmt.Sprintf("- 严重告警: %d条\n", criticalAlerts))
	summary.WriteString(fmt.Sprintf("- 警告告警: %d条\n", warningAlerts))

	if len(recentAlerts) > 0 {
		summary.WriteString("- 最近的活跃告警:\n")
		for _, alert := range recentAlerts {
			hostName := "未知主机"
			if alert.Host != nil {
				hostName = alert.Host.Name
			}
			summary.WriteString(fmt.Sprintf("  * [%s] %s - %s\n", alert.Level, alert.Title, hostName))
		}
	}

	return summary.String()
}

// getSystemStats 获取系统统计信息
func (s *aiService) getSystemStats() string {
	// 获取会话统计
	var totalSessions, activeSessions int64

	// 查询总会话数
	if err := s.db.Model(&model.ChatSession{}).Count(&totalSessions).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count total sessions")
		return ""
	}

	// 查询活跃会话数（最近24小时内有活动的）
	if err := s.db.Model(&model.ChatSession{}).Where("updated_at > ?", time.Now().Add(-24*time.Hour)).Count(&activeSessions).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count active sessions")
		return ""
	}

	// 获取消息统计
	var totalMessages int64
	if err := s.db.Model(&model.ChatMessage{}).Count(&totalMessages).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count total messages")
		return ""
	}

	// 获取最近的操作日志统计
	var recentOperations int64
	if err := s.db.Model(&model.OperationLog{}).Where("created_at > ?", time.Now().Add(-24*time.Hour)).Count(&recentOperations).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count recent operations")
		return ""
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("- 总对话会话: %d个\n", totalSessions))
	summary.WriteString(fmt.Sprintf("- 活跃会话(24h): %d个\n", activeSessions))
	summary.WriteString(fmt.Sprintf("- 总消息数: %d条\n", totalMessages))
	summary.WriteString(fmt.Sprintf("- 最近操作(24h): %d次\n", recentOperations))
	summary.WriteString(fmt.Sprintf("- 系统运行时间: %s\n", time.Since(time.Now().Add(-24*time.Hour)).Round(time.Hour)))

	return summary.String()
}

// ProcessMessageWithWorkflow 处理带工作流的消息
func (s *aiService) ProcessMessageWithWorkflow(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 首先分析是否需要工作流
	workflowIntent, err := s.AnalyzeWorkflowIntent(ctx, req.Message, req.SessionID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to analyze workflow intent, falling back to normal processing")
		return s.ProcessMessageWithTools(ctx, req)
	}

	// 如果需要工作流，在响应中包含工作流信息
	response, err := s.ProcessMessageWithTools(ctx, req)
	if err != nil {
		return nil, err
	}

	// 添加工作流相关信息到响应
	if workflowIntent.NeedsWorkflow {
		response.WorkflowSuggestion = &WorkflowSuggestion{
			WorkflowType:    workflowIntent.WorkflowType,
			SuggestedAction: workflowIntent.SuggestedAction,
			Parameters:      workflowIntent.Parameters,
			Confidence:      workflowIntent.Confidence,
		}
	}

	return response, nil
}

// AnalyzeWorkflowIntent 分析工作流意图
func (s *aiService) AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (*WorkflowIntentAnalysis, error) {
	systemPrompt := `你是一个智能运维助手，专门分析用户消息是否需要启动特定的工作流。

分析以下用户消息，判断是否需要启动工作流：

支持的工作流类型：
1. host_management - 主机管理（添加、查看、删除主机，测试连接）
2. command_execution - 命令执行工作流
3. system_monitoring - 系统监控工作流
4. alert_management - 告警管理工作流
5. report_generation - 报表生成工作流
6. backup_restore - 备份恢复工作流
7. security_audit - 安全审计工作流

请分析用户意图并返回JSON格式：
{
  "needs_workflow": true/false,
  "workflow_type": "工作流类型",
  "confidence": 0.0-1.0,
  "intent": "用户意图描述",
  "suggested_action": "建议的下一步操作",
  "parameters": {},
  "context": "上下文信息"
}

如果用户只是一般对话或询问，则needs_workflow为false。
只有当用户明确要执行某种运维操作时，才需要启动工作流。`

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: fmt.Sprintf("用户消息：%s", message)},
	}

	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze workflow intent: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	content := response.Choices[0].Message.Content

	// 尝试解析JSON
	var analysis WorkflowIntentAnalysis
	if err := json.Unmarshal([]byte(content), &analysis); err != nil {
		// 如果解析失败，返回默认结果
		return &WorkflowIntentAnalysis{
			NeedsWorkflow:   false,
			Confidence:      0.5,
			Intent:          "general_chat",
			SuggestedAction: "继续对话",
			Parameters:      make(map[string]interface{}),
			Context:         "一般对话",
		}, nil
	}

	return &analysis, nil
}

// GenerateWorkflowGuidance 生成工作流引导
func (s *aiService) GenerateWorkflowGuidance(ctx context.Context, workflowState *WorkflowState) (*WorkflowGuidance, error) {
	systemPrompt := `你是一个智能运维助手，负责为用户提供工作流引导。

根据当前工作流状态，生成友好的引导消息：

工作流状态信息：
- 工作流类型：%s
- 当前步骤：%s
- 状态：%s
- 进度：%.1f%%

请生成引导消息，包括：
1. 当前进展的友好描述
2. 下一步需要做什么
3. 具体的操作建议
4. 如果需要用户输入，说明输入要求

返回JSON格式：
{
  "message": "引导消息",
  "suggestions": ["建议1", "建议2", "建议3"],
  "next_action": "下一步操作",
  "required_inputs": ["需要的输入1", "需要的输入2"],
  "progress_indicator": "进度描述",
  "help_text": "帮助文本"
}`

	prompt := fmt.Sprintf(systemPrompt,
		workflowState.DefinitionID,
		workflowState.CurrentStep,
		workflowState.Status,
		workflowState.Progress*100)

	messages := []Message{
		{Role: "system", Content: prompt},
		{Role: "user", Content: "请生成当前工作流的引导信息"},
	}

	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to generate workflow guidance: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	content := response.Choices[0].Message.Content

	// 尝试解析JSON
	var guidance WorkflowGuidance
	if err := json.Unmarshal([]byte(content), &guidance); err != nil {
		// 如果解析失败，返回默认引导
		return &WorkflowGuidance{
			Message:           "工作流正在进行中，请继续提供所需信息。",
			Suggestions:       []string{"继续当前操作", "查看帮助", "取消工作流"},
			NextAction:        "等待用户输入",
			RequiredInputs:    []string{},
			ProgressIndicator: fmt.Sprintf("进度：%.1f%%", workflowState.Progress*100),
			HelpText:          "如需帮助，请输入'帮助'或'help'",
		}, nil
	}

	return &guidance, nil
}

// shouldAutoExecute 判断是否应该自动执行
func (s *aiService) shouldAutoExecute(intent *IntentResult) bool {
	// 检查意图类型是否支持自动执行
	autoExecuteIntents := []string{
		"host_status_diagnosis",
		"ssh_connection_test",
		"connection_diagnosis",
	}

	for _, autoIntent := range autoExecuteIntents {
		if intent.Type == autoIntent {
			// 检查置信度是否足够高
			return intent.Confidence >= 0.8
		}
	}

	return false
}

// processWithSmartExecution 使用智能执行处理消息
func (s *aiService) processWithSmartExecution(ctx context.Context, req *ProcessMessageRequest, intent *IntentResult, start time.Time) (*ProcessMessageResponse, error) {
	s.logger.WithFields(logrus.Fields{
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("Processing with smart execution")

	// 这里需要集成智能执行引擎
	// 暂时返回基础处理结果，后续完善
	executionResult := s.buildSmartExecutionResponse(intent, req.Message)

	return &ProcessMessageResponse{
		Content:        executionResult,
		TokenCount:     0,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
		Intent:         intent.Type,
		Confidence:     intent.Confidence,
		Parameters:     intent.Parameters,
	}, nil
}

// buildSmartExecutionResponse 构建智能执行响应
func (s *aiService) buildSmartExecutionResponse(intent *IntentResult, originalMessage string) string {
	switch intent.Type {
	case "host_status_diagnosis":
		if ip, exists := intent.Parameters["ip_address"]; exists {
			return fmt.Sprintf("🔍 正在诊断主机 %v 的状态...\n\n✅ 已自动执行以下检查：\n• 网络连通性测试\n• SSH端口状态检查\n• 主机连接测试\n\n📋 诊断结果：主机状态检查完成，详细信息请查看上方结果。", ip)
		}
		return "🔍 正在进行主机状态诊断，请稍候..."

	case "ssh_connection_test":
		if ip, exists := intent.Parameters["ip_address"]; exists {
			return fmt.Sprintf("🔧 正在测试到主机 %v 的SSH连接...\n\n✅ 已自动执行以下测试：\n• SSH端口连通性检查\n• SSH认证测试\n• 连接详细日志分析\n\n📋 测试结果：SSH连接测试完成，详细错误信息请查看上方结果。", ip)
		}
		return "🔧 正在进行SSH连接测试，请稍候..."

	case "connection_diagnosis":
		return "🔍 正在进行综合连接诊断...\n\n✅ 已自动执行：\n• 网络连通性测试\n• 端口状态检查\n• SSH连接测试\n• 服务状态诊断\n\n📋 诊断完成，详细结果请查看上方信息。"

	default:
		return fmt.Sprintf("🤖 已识别意图：%s，正在智能处理您的请求...", intent.Type)
	}
}

// createSuccessResponse 创建成功响应的辅助方法
func (s *aiService) createSuccessResponse(req *ProcessMessageRequest, intent *IntentResult, content string, start time.Time, extraData map[string]interface{}) (*ProcessMessageResponse, error) {
	// 更新上下文
	s.updateContextWithMessage(req.SessionID, "user", req.Message, intent.Parameters)

	contextData := map[string]interface{}{
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}
	for k, v := range extraData {
		contextData[k] = v
	}

	s.updateContextWithMessage(req.SessionID, "assistant", content, contextData)

	processingTime := time.Since(start)
	return &ProcessMessageResponse{
		Content:        content,
		Intent:         intent.Type,
		Confidence:     intent.Confidence,
		Parameters:     intent.Parameters,
		TokenCount:     50, // 估算token数
		ProcessingTime: processingTime,
		Timestamp:      time.Now(),
	}, nil
}

// HostAdditionResponse 主机添加响应
type HostAdditionResponse struct {
	Content   string `json:"content"`
	HostAdded bool   `json:"host_added"`
	HostID    *int64 `json:"host_id,omitempty"`
	Error     string `json:"error,omitempty"`
}

// HostListResponse 主机列表响应
type HostListResponse struct {
	Content   string `json:"content"`
	HostCount int    `json:"host_count"`
	Error     string `json:"error,omitempty"`
}

// SystemMonitoringResponse 系统监控响应
type SystemMonitoringResponse struct {
	Content    string `json:"content"`
	Action     string `json:"action"`
	MetricType string `json:"metric_type"`
	HostCount  int    `json:"host_count"`
	Success    bool   `json:"success"`
}

// ServiceManagementResponse 服务管理响应
type ServiceManagementResponse struct {
	Content string `json:"content"`
	Action  string `json:"action"`
	Service string `json:"service"`
	Success bool   `json:"success"`
}

// LogAnalysisResponse 日志分析响应
type LogAnalysisResponse struct {
	Content   string `json:"content"`
	Action    string `json:"action"`
	LogType   string `json:"log_type"`
	LineCount int    `json:"line_count"`
	Success   bool   `json:"success"`
}

// CommandExecutionResponse 命令执行响应
type CommandExecutionResponse struct {
	Content  string `json:"content"`
	Action   string `json:"action"`
	Command  string `json:"command"`
	ExitCode int    `json:"exit_code"`
	Success  bool   `json:"success"`
}

// AlertManagementResponse 告警管理响应
type AlertManagementResponse struct {
	Content    string `json:"content"`
	Action     string `json:"action"`
	AlertCount int    `json:"alert_count"`
	Success    bool   `json:"success"`
}

// handleHostAddition 处理主机添加
func (s *aiService) handleHostAddition(ctx context.Context, intent *IntentResult, userID int64) (*HostAdditionResponse, error) {
	// 提取主机信息
	ip, ipOk := intent.Parameters["ip"].(string)
	username, usernameOk := intent.Parameters["username"].(string)
	password, passwordOk := intent.Parameters["password"].(string)

	// 如果缺少必要信息，返回提示
	if !ipOk || !usernameOk || !passwordOk {
		return &HostAdditionResponse{
			Content:   "请提供完整的主机信息，格式：IP地址 用户名 密码\n例如：************* root mypassword",
			HostAdded: false,
		}, nil
	}

	// 验证IP地址格式
	if net.ParseIP(ip) == nil {
		return &HostAdditionResponse{
			Content:   fmt.Sprintf("IP地址格式不正确：%s\n请提供有效的IP地址", ip),
			HostAdded: false,
		}, nil
	}

	// 创建主机添加请求
	hostReq := &model.HostCreateRequest{
		Name:              fmt.Sprintf("主机-%s", ip),
		IPAddress:         ip,
		Port:              22, // 默认SSH端口
		Username:          username,
		Password:          password,
		Environment:       "production", // 默认环境
		Description:       fmt.Sprintf("通过AI对话添加的主机 - %s", time.Now().Format("2006-01-02 15:04:05")),
		Tags:              []string{"ai-added", "auto"},
		MonitoringEnabled: true,
		BackupEnabled:     false,
		CreatedBy:         userID,
	}

	// 调用主机服务添加主机
	hostResp, err := s.hostService.CreateHost(hostReq)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"ip":       ip,
			"username": username,
			"user_id":  userID,
		}).Error("Failed to add host")

		return &HostAdditionResponse{
			Content:   fmt.Sprintf("添加主机失败：%s\n请检查主机信息是否正确，或联系管理员", err.Error()),
			HostAdded: false,
			Error:     err.Error(),
		}, nil
	}

	// 测试连接
	testResult, err := s.hostService.TestConnection(hostResp.ID)
	connectionStatus := "连接测试失败"
	if err == nil && testResult.Success {
		connectionStatus = "连接测试成功"
	}

	s.logger.WithFields(logrus.Fields{
		"host_id":           hostResp.ID,
		"ip":                ip,
		"username":          username,
		"user_id":           userID,
		"connection_status": connectionStatus,
	}).Info("Host added successfully via AI chat")

	return &HostAdditionResponse{
		Content: fmt.Sprintf(`✅ 主机添加成功！

📋 主机信息：
• 主机ID：%d
• IP地址：%s
• 用户名：%s
• 端口：%d
• 状态：%s

🔍 连接测试：%s

您现在可以通过主机管理界面查看和管理这台主机。`,
			hostResp.ID, ip, username, 22, connectionStatus, connectionStatus),
		HostAdded: true,
		HostID:    &hostResp.ID,
	}, nil
}

// handleHostList 处理主机列表查询
func (s *aiService) handleHostList(ctx context.Context, intent *IntentResult, userID int64) (*HostListResponse, error) {
	// 构建查询参数
	query := &model.HostListQuery{
		Page:  1,
		Limit: 50, // 默认显示前50台主机
	}

	// 检查是否有过滤条件
	if filter, ok := intent.Parameters["filter"].(string); ok {
		switch filter {
		case "status":
			// 如果需要状态过滤，可以在这里添加
		case "account":
			// 账号信息过滤
		case "info":
			// 信息过滤
		}
	}

	// 调用主机服务获取主机列表
	hostListResp, err := s.hostService.ListHosts(query)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to list hosts")
		return &HostListResponse{
			Content:   "获取主机列表失败，请稍后重试或联系管理员",
			HostCount: 0,
			Error:     err.Error(),
		}, nil
	}

	// 构建响应内容
	if len(hostListResp.Hosts) == 0 {
		return &HostListResponse{
			Content:   "📋 当前没有已添加的主机\n\n💡 您可以通过以下方式添加主机：\n• 输入：添加主机 IP地址 用户名 密码\n• 例如：添加主机 ************* root mypassword",
			HostCount: 0,
		}, nil
	}

	// 格式化主机列表
	var content strings.Builder
	content.WriteString("📋 主机列表：\n\n")

	for i, host := range hostListResp.Hosts {
		statusIcon := "🔴"
		switch host.Status {
		case "online":
			statusIcon = "🟢"
		case "offline":
			statusIcon = "🔴"
		case "unknown":
			statusIcon = "🟡"
		case "error":
			statusIcon = "❌"
		case "maintenance":
			statusIcon = "🔧"
		}

		content.WriteString(fmt.Sprintf("%d. %s **%s** (%s)\n", i+1, statusIcon, host.Name, host.IPAddress))
		content.WriteString(fmt.Sprintf("   • 用户名：%s\n", host.Username))
		content.WriteString(fmt.Sprintf("   • 端口：%d\n", host.Port))
		content.WriteString(fmt.Sprintf("   • 环境：%s\n", host.Environment))
		if host.Description != "" {
			content.WriteString(fmt.Sprintf("   • 描述：%s\n", host.Description))
		}
		content.WriteString(fmt.Sprintf("   • 创建时间：%s\n", host.CreatedAt.Format("2006-01-02 15:04:05")))
		if host.LastConnected != nil {
			content.WriteString(fmt.Sprintf("   • 最后连接：%s\n", host.LastConnected.Format("2006-01-02 15:04:05")))
		}
		content.WriteString("\n")
	}

	// 添加统计信息
	onlineCount := 0
	offlineCount := 0
	for _, host := range hostListResp.Hosts {
		if host.Status == "online" {
			onlineCount++
		} else {
			offlineCount++
		}
	}

	content.WriteString(fmt.Sprintf("📊 统计信息：\n"))
	content.WriteString(fmt.Sprintf("• 总计：%d 台主机\n", len(hostListResp.Hosts)))
	content.WriteString(fmt.Sprintf("• 在线：%d 台\n", onlineCount))
	content.WriteString(fmt.Sprintf("• 离线：%d 台\n", offlineCount))

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"host_count": len(hostListResp.Hosts),
		"online":     onlineCount,
		"offline":    offlineCount,
	}).Info("Host list retrieved via AI chat")

	return &HostListResponse{
		Content:   content.String(),
		HostCount: len(hostListResp.Hosts),
	}, nil
}

// handleSystemMonitoring 处理系统监控
func (s *aiService) handleSystemMonitoring(ctx context.Context, intent *IntentResult, userID int64) (*SystemMonitoringResponse, error) {
	// 提取监控参数
	metric, _ := intent.Parameters["metric"].(string)
	action, _ := intent.Parameters["action"].(string)
	host, _ := intent.Parameters["host"].(string)
	scope, _ := intent.Parameters["scope"].(string)

	if metric == "" {
		metric = "system" // 默认系统指标
	}
	if action == "" {
		action = "check" // 默认检查动作
	}

	// 获取目标主机
	var targetHosts []*model.Host
	var err error

	if host != "" {
		// 指定主机
		if net.ParseIP(host) != nil {
			// 按IP查找
			targetHosts, err = s.getHostsByIP(host)
		} else {
			// 按名称查找
			targetHosts, err = s.getHostsByName(host)
		}
	} else if scope == "all" {
		// 所有主机
		targetHosts, err = s.getAllActiveHosts()
	} else {
		// 默认获取前5台活跃主机
		targetHosts, err = s.getDefaultHosts(5)
	}

	if err != nil {
		return &SystemMonitoringResponse{
			Content: fmt.Sprintf("获取主机信息失败：%s", err.Error()),
			Action:  action,
			Success: false,
		}, nil
	}

	if len(targetHosts) == 0 {
		return &SystemMonitoringResponse{
			Content: "未找到可用的主机，请先添加主机或检查主机状态",
			Action:  action,
			Success: false,
		}, nil
	}

	// 执行监控
	results := make([]string, 0, len(targetHosts))
	for _, targetHost := range targetHosts {
		result, err := s.executeMonitoringCommand(ctx, targetHost, metric)
		if err != nil {
			results = append(results, fmt.Sprintf("❌ %s (%s): %s", targetHost.Name, targetHost.IPAddress, err.Error()))
		} else {
			results = append(results, result)
		}
	}

	// 格式化响应
	content := fmt.Sprintf("📊 %s监控结果：\n\n%s",
		s.getMetricDisplayName(metric),
		strings.Join(results, "\n"))

	s.logger.WithFields(logrus.Fields{
		"metric":     metric,
		"action":     action,
		"host_count": len(targetHosts),
		"user_id":    userID,
	}).Info("System monitoring executed via AI chat")

	return &SystemMonitoringResponse{
		Content:    content,
		Action:     action,
		MetricType: metric,
		HostCount:  len(targetHosts),
		Success:    true,
	}, nil
}

// 主机查询辅助方法
func (s *aiService) getHostsByIP(ip string) ([]*model.Host, error) {
	var hosts []*model.Host
	err := s.db.Where("ip_address = ? AND status != ?", ip, "deleted").Find(&hosts).Error
	return hosts, err
}

func (s *aiService) getHostsByName(name string) ([]*model.Host, error) {
	var hosts []*model.Host
	err := s.db.Where("name LIKE ? AND status != ?", "%"+name+"%", "deleted").Find(&hosts).Error
	return hosts, err
}

func (s *aiService) getAllActiveHosts() ([]*model.Host, error) {
	var hosts []*model.Host
	err := s.db.Where("status IN ?", []string{"online", "offline"}).Find(&hosts).Error
	return hosts, err
}

func (s *aiService) getDefaultHosts(limit int) ([]*model.Host, error) {
	var hosts []*model.Host
	err := s.db.Where("status IN ?", []string{"online", "offline"}).Limit(limit).Find(&hosts).Error
	return hosts, err
}

// executeMonitoringCommand 执行监控命令
func (s *aiService) executeMonitoringCommand(ctx context.Context, host *model.Host, metric string) (string, error) {
	var command string

	switch metric {
	case "cpu":
		command = "top -bn1 | head -3 | tail -1 | awk '{print $2}' | sed 's/%us,//' | awk '{printf \"CPU使用率: %.1f%%\", $1}'"
	case "memory":
		command = "free -h | awk 'NR==2{printf \"内存使用: %s/%s (%.1f%%)\", $3,$2,$3*100/$2}'"
	case "disk":
		command = "df -h | awk '$NF==\"/\"{printf \"磁盘使用: %s/%s (%s)\", $3,$2,$5}'"
	case "load":
		command = "uptime | awk -F'load average:' '{print \"系统负载:\" $2}'"
	default:
		// 默认获取系统概览
		command = "echo '=== 系统概览 ===' && uptime && echo '' && free -h | head -2 && echo '' && df -h | head -2"
	}

	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 15,
	}

	result, err := s.hostService.ExecuteCommand(host.ID, req)
	if err != nil {
		return "", fmt.Errorf("执行监控命令失败: %w", err)
	}

	if result.ExitCode != 0 {
		return "", fmt.Errorf("命令执行失败，退出码: %d", result.ExitCode)
	}

	return fmt.Sprintf("✅ %s (%s):\n%s", host.Name, host.IPAddress, result.Stdout), nil
}

// getMetricDisplayName 获取指标显示名称
func (s *aiService) getMetricDisplayName(metric string) string {
	switch metric {
	case "cpu":
		return "CPU"
	case "memory":
		return "内存"
	case "disk":
		return "磁盘"
	case "load":
		return "系统负载"
	default:
		return "系统"
	}
}

// handleServiceManagement 处理服务管理
func (s *aiService) handleServiceManagement(ctx context.Context, intent *IntentResult, userID int64) (*ServiceManagementResponse, error) {
	action, _ := intent.Parameters["action"].(string)
	service, _ := intent.Parameters["service"].(string)
	host, _ := intent.Parameters["host"].(string)

	if action == "" || service == "" {
		return &ServiceManagementResponse{
			Content: "请指定要执行的操作和服务名称，例如：重启nginx服务",
			Success: false,
		}, nil
	}

	// 获取目标主机
	var targetHosts []*model.Host
	var err error

	if host != "" {
		if net.ParseIP(host) != nil {
			targetHosts, err = s.getHostsByIP(host)
		} else {
			targetHosts, err = s.getHostsByName(host)
		}
	} else {
		// 默认使用第一台活跃主机
		targetHosts, err = s.getDefaultHosts(1)
	}

	if err != nil || len(targetHosts) == 0 {
		return &ServiceManagementResponse{
			Content: "未找到可用的主机，请指定主机或检查主机状态",
			Action:  action,
			Service: service,
			Success: false,
		}, nil
	}

	targetHost := targetHosts[0]

	// 构建服务管理命令
	var command string
	switch action {
	case "start", "启动":
		command = fmt.Sprintf("sudo systemctl start %s && echo '服务启动成功'", service)
	case "stop", "停止":
		command = fmt.Sprintf("sudo systemctl stop %s && echo '服务停止成功'", service)
	case "restart", "重启":
		command = fmt.Sprintf("sudo systemctl restart %s && echo '服务重启成功'", service)
	case "status", "状态":
		command = fmt.Sprintf("sudo systemctl status %s", service)
	default:
		return &ServiceManagementResponse{
			Content: fmt.Sprintf("不支持的操作：%s。支持的操作：启动、停止、重启、状态", action),
			Action:  action,
			Service: service,
			Success: false,
		}, nil
	}

	// 执行命令
	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 30,
	}

	result, err := s.hostService.ExecuteCommand(targetHost.ID, req)
	if err != nil {
		return &ServiceManagementResponse{
			Content: fmt.Sprintf("❌ 在主机 %s 上执行服务管理命令失败：%s", targetHost.Name, err.Error()),
			Action:  action,
			Service: service,
			Success: false,
		}, nil
	}

	var content string
	if result.ExitCode == 0 {
		content = fmt.Sprintf("✅ 在主机 %s 上%s %s 服务成功\n\n输出：\n%s",
			targetHost.Name, s.getActionDisplayName(action), service, result.Stdout)
	} else {
		content = fmt.Sprintf("❌ 在主机 %s 上%s %s 服务失败\n\n错误：\n%s",
			targetHost.Name, s.getActionDisplayName(action), service, result.Stderr)
	}

	s.logger.WithFields(logrus.Fields{
		"action":    action,
		"service":   service,
		"host_id":   targetHost.ID,
		"exit_code": result.ExitCode,
		"user_id":   userID,
	}).Info("Service management executed via AI chat")

	return &ServiceManagementResponse{
		Content: content,
		Action:  action,
		Service: service,
		Success: result.ExitCode == 0,
	}, nil
}

// getActionDisplayName 获取操作显示名称
func (s *aiService) getActionDisplayName(action string) string {
	switch action {
	case "start", "启动":
		return "启动"
	case "stop", "停止":
		return "停止"
	case "restart", "重启":
		return "重启"
	case "status", "状态":
		return "查看状态"
	default:
		return action
	}
}

// handleLogAnalysis 处理日志分析
func (s *aiService) handleLogAnalysis(ctx context.Context, intent *IntentResult, userID int64) (*LogAnalysisResponse, error) {
	action, _ := intent.Parameters["action"].(string)
	logType, _ := intent.Parameters["log_type"].(string)
	keyword, _ := intent.Parameters["keyword"].(string)
	host, _ := intent.Parameters["host"].(string)

	if action == "" {
		action = "view"
	}
	if logType == "" && keyword == "" {
		logType = "system"
	}

	// 获取目标主机
	var targetHosts []*model.Host
	var err error

	if host != "" {
		if net.ParseIP(host) != nil {
			targetHosts, err = s.getHostsByIP(host)
		} else {
			targetHosts, err = s.getHostsByName(host)
		}
	} else {
		targetHosts, err = s.getDefaultHosts(1)
	}

	if err != nil || len(targetHosts) == 0 {
		return &LogAnalysisResponse{
			Content: "未找到可用的主机，请指定主机或检查主机状态",
			Action:  action,
			Success: false,
		}, nil
	}

	targetHost := targetHosts[0]

	// 构建日志查询命令
	var command string
	if keyword != "" {
		command = fmt.Sprintf("grep -i '%s' /var/log/syslog | tail -20", keyword)
	} else {
		switch logType {
		case "error":
			command = "tail -50 /var/log/syslog | grep -i error"
		case "warning":
			command = "tail -50 /var/log/syslog | grep -i warning"
		case "auth":
			command = "tail -20 /var/log/auth.log"
		default:
			command = "tail -20 /var/log/syslog"
		}
	}

	// 执行命令
	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 15,
	}

	result, err := s.hostService.ExecuteCommand(targetHost.ID, req)
	if err != nil {
		return &LogAnalysisResponse{
			Content: fmt.Sprintf("❌ 在主机 %s 上查询日志失败：%s", targetHost.Name, err.Error()),
			Action:  action,
			LogType: logType,
			Success: false,
		}, nil
	}

	var content string
	if result.ExitCode == 0 {
		lines := strings.Split(strings.TrimSpace(result.Stdout), "\n")
		content = fmt.Sprintf("📋 主机 %s 的%s日志（最近%d条）：\n\n%s",
			targetHost.Name, s.getLogTypeDisplayName(logType, keyword), len(lines), result.Stdout)
	} else {
		content = fmt.Sprintf("❌ 在主机 %s 上查询日志失败\n\n错误：\n%s",
			targetHost.Name, result.Stderr)
	}

	return &LogAnalysisResponse{
		Content:   content,
		Action:    action,
		LogType:   logType,
		LineCount: len(strings.Split(strings.TrimSpace(result.Stdout), "\n")),
		Success:   result.ExitCode == 0,
	}, nil
}

// handleCommandExecution 处理命令执行
func (s *aiService) handleCommandExecution(ctx context.Context, intent *IntentResult, userID int64) (*CommandExecutionResponse, error) {
	command, _ := intent.Parameters["command"].(string)
	host, _ := intent.Parameters["host"].(string)

	if command == "" {
		return &CommandExecutionResponse{
			Content: "请指定要执行的命令",
			Action:  "execute",
			Success: false,
		}, nil
	}

	// 安全检查
	if !s.isCommandSafe(command) {
		return &CommandExecutionResponse{
			Content: fmt.Sprintf("⚠️ 命令 '%s' 可能存在安全风险，已被阻止执行", command),
			Action:  "execute",
			Command: command,
			Success: false,
		}, nil
	}

	// 获取目标主机
	var targetHosts []*model.Host
	var err error

	if host != "" {
		if net.ParseIP(host) != nil {
			targetHosts, err = s.getHostsByIP(host)
		} else {
			targetHosts, err = s.getHostsByName(host)
		}
	} else {
		targetHosts, err = s.getDefaultHosts(1)
	}

	if err != nil || len(targetHosts) == 0 {
		return &CommandExecutionResponse{
			Content: "未找到可用的主机，请指定主机或检查主机状态",
			Action:  "execute",
			Command: command,
			Success: false,
		}, nil
	}

	targetHost := targetHosts[0]

	// 执行命令
	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 30,
	}

	result, err := s.hostService.ExecuteCommand(targetHost.ID, req)
	if err != nil {
		return &CommandExecutionResponse{
			Content: fmt.Sprintf("❌ 在主机 %s 上执行命令失败：%s", targetHost.Name, err.Error()),
			Action:  "execute",
			Command: command,
			Success: false,
		}, nil
	}

	content := fmt.Sprintf("💻 在主机 %s 上执行命令：%s\n\n", targetHost.Name, command)
	if result.ExitCode == 0 {
		content += fmt.Sprintf("✅ 执行成功（退出码：%d）\n\n输出：\n%s", result.ExitCode, result.Stdout)
	} else {
		content += fmt.Sprintf("❌ 执行失败（退出码：%d）\n\n错误：\n%s", result.ExitCode, result.Stderr)
	}

	return &CommandExecutionResponse{
		Content:  content,
		Action:   "execute",
		Command:  command,
		ExitCode: result.ExitCode,
		Success:  result.ExitCode == 0,
	}, nil
}

// handleAlertManagement 处理告警管理
func (s *aiService) handleAlertManagement(ctx context.Context, intent *IntentResult, userID int64) (*AlertManagementResponse, error) {
	action, _ := intent.Parameters["action"].(string)
	alertID, _ := intent.Parameters["alert_id"].(string)
	status, _ := intent.Parameters["status"].(string)

	if action == "" {
		action = "list"
	}

	switch action {
	case "list", "查看":
		// 查看告警列表
		var alerts []model.Alert
		query := s.db.Model(&model.Alert{})

		if status == "active" || status == "" {
			query = query.Where("status IN ?", []string{"open", "acknowledged"})
		} else {
			query = query.Where("status = ?", status)
		}

		err := query.Order("created_at DESC").Limit(10).Find(&alerts).Error
		if err != nil {
			return &AlertManagementResponse{
				Content: fmt.Sprintf("查询告警失败：%s", err.Error()),
				Action:  action,
				Success: false,
			}, nil
		}

		if len(alerts) == 0 {
			return &AlertManagementResponse{
				Content:    "🎉 当前没有活跃的告警",
				Action:     action,
				AlertCount: 0,
				Success:    true,
			}, nil
		}

		content := fmt.Sprintf("🚨 当前告警列表（共%d条）：\n\n", len(alerts))
		for _, alert := range alerts {
			hostIDStr := "未知"
			if alert.HostID != nil {
				hostIDStr = fmt.Sprintf("%d", *alert.HostID)
			}
			content += fmt.Sprintf("• [%s] %s - %s\n  主机：%s | 级别：%s | 时间：%s\n\n",
				alert.Status, alert.Title, alert.Message,
				hostIDStr, alert.Level, alert.AlertTime.Format("2006-01-02 15:04:05"))
		}

		return &AlertManagementResponse{
			Content:    content,
			Action:     action,
			AlertCount: len(alerts),
			Success:    true,
		}, nil

	case "acknowledge", "确认":
		if alertID == "" {
			return &AlertManagementResponse{
				Content: "请指定要确认的告警ID",
				Action:  action,
				Success: false,
			}, nil
		}

		// 确认告警
		result := s.db.Model(&model.Alert{}).Where("id = ?", alertID).Update("status", "acknowledged")
		if result.Error != nil {
			return &AlertManagementResponse{
				Content: fmt.Sprintf("确认告警失败：%s", result.Error.Error()),
				Action:  action,
				Success: false,
			}, nil
		}

		if result.RowsAffected == 0 {
			return &AlertManagementResponse{
				Content: fmt.Sprintf("未找到ID为 %s 的告警", alertID),
				Action:  action,
				Success: false,
			}, nil
		}

		return &AlertManagementResponse{
			Content:    fmt.Sprintf("✅ 已确认告警 %s", alertID),
			Action:     action,
			AlertCount: 1,
			Success:    true,
		}, nil

	default:
		return &AlertManagementResponse{
			Content: fmt.Sprintf("不支持的操作：%s。支持的操作：查看、确认", action),
			Action:  action,
			Success: false,
		}, nil
	}
}

// 辅助方法
func (s *aiService) getLogTypeDisplayName(logType, keyword string) string {
	if keyword != "" {
		return fmt.Sprintf("包含'%s'的", keyword)
	}
	switch logType {
	case "error":
		return "错误"
	case "warning":
		return "警告"
	case "auth":
		return "认证"
	case "system":
		return "系统"
	default:
		return logType
	}
}

func (s *aiService) isCommandSafe(command string) bool {
	// 危险命令列表
	dangerousCommands := []string{
		"rm -rf", "dd if=", "mkfs", "fdisk", "parted",
		"shutdown", "reboot", "halt", "poweroff",
		"passwd", "userdel", "groupdel",
		"iptables -F", "ufw --force",
		"chmod 777", "chown -R",
		"wget", "curl", "nc -l",
	}

	commandLower := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		if strings.Contains(commandLower, dangerous) {
			return false
		}
	}

	// 允许的安全命令
	safeCommands := []string{
		"ps", "top", "htop", "free", "df", "du",
		"ls", "cat", "head", "tail", "grep", "awk", "sed",
		"uptime", "who", "w", "id", "whoami",
		"netstat", "ss", "lsof", "iostat", "vmstat",
		"systemctl status", "service status",
	}

	for _, safe := range safeCommands {
		if strings.HasPrefix(commandLower, safe) {
			return true
		}
	}

	return false
}

// handleCommandExecutionFromAction 从SmartPendingAction处理命令执行
func (s *aiService) handleCommandExecutionFromAction(ctx context.Context, action *SmartPendingAction, userID int64) (string, error) {
	// 从action中提取参数
	hostIP, _ := action.Parameters["host_ip"].(string)
	command, _ := action.Parameters["command"].(string)

	s.logger.WithFields(logrus.Fields{
		"user_id":   userID,
		"host_ip":   hostIP,
		"command":   command,
		"action_id": action.ID,
	}).Info("Executing command from pending action")

	// 验证参数
	if hostIP == "" {
		return "", fmt.Errorf("主机IP地址不能为空")
	}
	if command == "" {
		return "", fmt.Errorf("命令不能为空")
	}

	// 1. 命令安全性检查
	permissionSvc := NewMockPermissionService(s.logger)

	// 使用简化的安全检查
	checker := security.NewCommandSecurityChecker(s.logger)
	securityResult := checker.CheckCommand(command)

	s.logger.WithFields(logrus.Fields{
		"command":    command,
		"risk_level": securityResult.RiskLevel,
		"is_allowed": securityResult.IsAllowed,
	}).Info("Command security validation completed")

	// 如果命令被安全策略阻止
	if !securityResult.IsAllowed {
		return securityResult.SecurityWarning, fmt.Errorf("命令被安全策略阻止")
	}

	// 检查用户权限
	canExecute, err := permissionSvc.CanExecuteCommand(userID, 0, command) // hostID暂时用0
	if err != nil {
		return "", fmt.Errorf("权限检查失败: %w", err)
	}

	if !canExecute {
		return "❌ **权限不足**: 您没有执行此命令的权限", fmt.Errorf("用户权限不足")
	}

	// 查找主机
	hostListReq := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}
	hostListResp, err := s.hostService.ListHosts(hostListReq)
	if err != nil {
		return "", fmt.Errorf("查询主机列表失败: %w", err)
	}

	var targetHost *model.HostResponse
	for _, host := range hostListResp.Hosts {
		if host.IPAddress == hostIP {
			targetHost = host
			break
		}
	}

	if targetHost == nil {
		return "", fmt.Errorf("找不到IP地址为 %s 的主机，请先添加该主机", hostIP)
	}

	// 创建命令执行请求
	cmdReq := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 30, // 30秒超时
	}

	// 执行命令
	result, err := s.hostService.ExecuteCommand(targetHost.ID, cmdReq)
	if err != nil {
		return "", fmt.Errorf("命令执行失败: %w", err)
	}

	// 格式化输出
	var output strings.Builder
	output.WriteString(fmt.Sprintf("✅ **命令执行成功**\n\n"))
	output.WriteString(fmt.Sprintf("🖥️ **主机**: %s (%s)\n", targetHost.IPAddress, targetHost.Name))
	output.WriteString(fmt.Sprintf("📝 **命令**: `%s`\n", command))
	output.WriteString(fmt.Sprintf("⏱️ **执行时间**: %dms\n", result.Duration))
	output.WriteString(fmt.Sprintf("🔢 **退出码**: %d\n\n", result.ExitCode))

	if result.Stdout != "" {
		output.WriteString("📤 **输出结果**:\n```\n")
		output.WriteString(result.Stdout)
		output.WriteString("\n```\n")
	}

	if result.Stderr != "" {
		output.WriteString("\n⚠️ **错误信息**:\n```\n")
		output.WriteString(result.Stderr)
		output.WriteString("\n```\n")
	}

	if result.ExitCode != 0 {
		output.WriteString(fmt.Sprintf("\n❌ **注意**: 命令执行完成但退出码为 %d，可能存在错误", result.ExitCode))
	}

	return output.String(), nil
}
