package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

func main() {
	baseURL := "http://localhost:8080"

	fmt.Println("🧪 智能对话系统边界情况测试")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 测试场景1：参数不完整的主机添加
	testIncompleteParams(baseURL)

	// 测试场景2：拒绝操作
	testRejection(baseURL)

	// 测试场景3：无效IP地址
	testInvalidIP(baseURL)

	// 测试场景4：分步参数收集
	testStepByStep(baseURL)

	fmt.Println("\n🎯 所有边界情况测试完成！")
}

func testIncompleteParams(baseURL string) {
	fmt.Println("\n📋 测试场景1：参数不完整的主机添加")

	sessionID := createSession(baseURL, "参数不完整测试")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	message := "主机添加"
	fmt.Printf("📤 用户: %s\n", message)

	response := sendMessage(baseURL, sessionID, message)
	fmt.Printf("🤖 AI: %s\n", response.Data.AIResponse)

	if strings.Contains(response.Data.AIResponse, "请提供") || strings.Contains(response.Data.AIResponse, "IP地址") {
		fmt.Println("✅ AI正确提示需要更多参数")
	} else {
		fmt.Println("⚠️ AI没有正确处理参数不完整的情况")
	}
}

func testRejection(baseURL string) {
	fmt.Println("\n📋 测试场景2：用户拒绝操作")

	sessionID := createSession(baseURL, "拒绝操作测试")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	// 第一步：发送主机添加请求
	message1 := "添加主机 ********** admin testpass"
	fmt.Printf("📤 用户: %s\n", message1)

	response1 := sendMessage(baseURL, sessionID, message1)
	fmt.Printf("🤖 AI: %s\n", response1.Data.AIResponse)

	if strings.Contains(response1.Data.AIResponse, "是否要添加主机") {
		// 第二步：用户拒绝
		time.Sleep(1 * time.Second)
		message2 := "否"
		fmt.Printf("📤 用户: %s\n", message2)

		response2 := sendMessage(baseURL, sessionID, message2)
		fmt.Printf("🤖 AI: %s\n", response2.Data.AIResponse)

		if strings.Contains(response2.Data.AIResponse, "取消") || strings.Contains(response2.Data.AIResponse, "已取消") {
			fmt.Println("✅ AI正确处理用户拒绝")
		} else {
			fmt.Println("⚠️ AI没有正确处理用户拒绝")
		}
	}
}

func testInvalidIP(baseURL string) {
	fmt.Println("\n📋 测试场景3：无效IP地址")

	sessionID := createSession(baseURL, "无效IP测试")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	message := "添加主机 999.999.999.999 root password"
	fmt.Printf("📤 用户: %s\n", message)

	response := sendMessage(baseURL, sessionID, message)
	fmt.Printf("🤖 AI: %s\n", response.Data.AIResponse)

	// 注意：当前的模拟AI可能不会验证IP格式，这是一个改进点
	fmt.Println("ℹ️ 当前模拟AI不验证IP格式，这是一个可以改进的地方")
}

func testStepByStep(baseURL string) {
	fmt.Println("\n📋 测试场景4：分步参数收集")

	sessionID := createSession(baseURL, "分步收集测试")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	// 第一步：只说要添加主机
	message1 := "主机添加"
	fmt.Printf("📤 用户: %s\n", message1)

	response1 := sendMessage(baseURL, sessionID, message1)
	fmt.Printf("🤖 AI: %s\n", response1.Data.AIResponse)

	// 第二步：提供参数
	time.Sleep(1 * time.Second)
	message2 := "************ ubuntu mypass123"
	fmt.Printf("📤 用户: %s\n", message2)

	response2 := sendMessage(baseURL, sessionID, message2)
	fmt.Printf("🤖 AI: %s\n", response2.Data.AIResponse)

	if strings.Contains(response2.Data.AIResponse, "是否要添加主机") {
		fmt.Println("✅ 分步参数收集工作正常")

		// 第三步：确认
		time.Sleep(1 * time.Second)
		message3 := "是"
		fmt.Printf("📤 用户: %s\n", message3)

		response3 := sendMessage(baseURL, sessionID, message3)
		fmt.Printf("🤖 AI: %s\n", response3.Data.AIResponse)

		if strings.Contains(response3.Data.AIResponse, "添加成功") || strings.Contains(response3.Data.AIResponse, "✅") {
			fmt.Println("✅ 分步流程完整执行成功")
		}
	} else {
		fmt.Println("⚠️ 分步参数收集可能有问题")
	}
}

type SessionResponse struct {
	Code int `json:"code"`
	Data struct {
		SessionID string `json:"session_id"`
	} `json:"data"`
}

type MessageResponse struct {
	Code int `json:"code"`
	Data struct {
		AIResponse string `json:"ai_response"`
		Intent     string `json:"intent"`
	} `json:"data"`
}

func createSession(baseURL, title string) string {
	url := baseURL + "/api/v1/chat/sessions"

	reqBody := map[string]string{"title": title}
	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建会话失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var sessionResp SessionResponse
	if err := json.Unmarshal(body, &sessionResp); err != nil {
		fmt.Printf("解析会话响应失败: %v\n", err)
		return ""
	}

	return sessionResp.Data.SessionID
}

func sendMessage(baseURL, sessionID, content string) *MessageResponse {
	url := baseURL + "/api/v1/chat/message"

	reqBody := map[string]interface{}{
		"session_id": sessionID,
		"content":    content,
		"stream":     false,
	}

	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("发送消息失败: %v\n", err)
		return &MessageResponse{}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var msgResp MessageResponse
	if err := json.Unmarshal(body, &msgResp); err != nil {
		fmt.Printf("解析消息响应失败: %v\n", err)
		return &MessageResponse{}
	}

	return &msgResp
}
