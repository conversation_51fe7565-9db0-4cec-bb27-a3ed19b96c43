package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试主机管理修复后的功能
func main() {
	baseURL := "http://localhost:8766/api/v1"

	fmt.Println("=== 测试主机管理功能修复 ===")

	// 测试1：添加主机（仅密码认证）
	fmt.Println("\n1. 测试添加主机（密码认证）...")
	testAddHostWithPassword(baseURL)

	// 测试2：添加主机（仅SSH密钥认证）
	fmt.Println("\n2. 测试添加主机（SSH密钥认证）...")
	testAddHostWithSSHKey(baseURL)

	// 测试3：添加主机（无认证方式 - 应该失败）
	fmt.Println("\n3. 测试添加主机（无认证方式）...")
	testAddHostWithoutAuth(baseURL)

	// 测试4：添加重复主机名（应该失败）
	fmt.Println("\n4. 测试添加重复主机名...")
	testAddDuplicateHost(baseURL)

	// 测试5：查询主机列表
	fmt.Println("\n5. 测试查询主机列表...")
	testListHosts(baseURL)

	// 测试6：测试主机连接
	fmt.Println("\n6. 测试主机连接...")
	testHostConnection(baseURL)
}

func testAddHostWithPassword(baseURL string) {
	data := map[string]interface{}{
		"name":        "test-password-host",
		"ip_address":  "*************",
		"port":        22,
		"username":    "testuser",
		"password":    "testpassword123",
		"description": "测试密码认证主机",
		"environment": "testing",
	}

	response := makeRequest("POST", baseURL+"/hosts", data)
	if response != nil {
		fmt.Printf("响应: %s\n", response)
	}
}

func testAddHostWithSSHKey(baseURL string) {
	data := map[string]interface{}{
		"name":               "test-sshkey-host",
		"ip_address":         "*************",
		"port":               22,
		"username":           "testuser",
		"ssh_key_path":       "/home/<USER>/.ssh/id_rsa",
		"ssh_key_passphrase": "keypassword",
		"description":        "测试SSH密钥认证主机",
		"environment":        "testing",
	}

	response := makeRequest("POST", baseURL+"/hosts", data)
	if response != nil {
		fmt.Printf("响应: %s\n", response)
	}
}

func testAddHostWithoutAuth(baseURL string) {
	data := map[string]interface{}{
		"name":        "test-noauth-host",
		"ip_address":  "*************",
		"port":        22,
		"username":    "testuser",
		"description": "测试无认证方式主机（应该失败）",
		"environment": "testing",
	}

	response := makeRequest("POST", baseURL+"/hosts", data)
	if response != nil {
		fmt.Printf("响应: %s\n", response)
	}
}

func testAddDuplicateHost(baseURL string) {
	data := map[string]interface{}{
		"name":        "test-password-host", // 重复的主机名
		"ip_address":  "*************",
		"port":        22,
		"username":    "testuser",
		"password":    "testpassword123",
		"description": "测试重复主机名（应该失败）",
		"environment": "testing",
	}

	response := makeRequest("POST", baseURL+"/hosts", data)
	if response != nil {
		fmt.Printf("响应: %s\n", response)
	}
}

func testListHosts(baseURL string) {
	response := makeRequest("GET", baseURL+"/hosts", nil)
	if response != nil {
		fmt.Printf("主机列表响应: %s\n", response)
	}
}

func testHostConnection(baseURL string) {
	// 假设第一个主机的ID是1，实际应该从列表中获取
	response := makeRequest("POST", baseURL+"/hosts/1/test", nil)
	if response != nil {
		fmt.Printf("连接测试响应: %s\n", response)
	}
}

func makeRequest(method, url string, data interface{}) []byte {
	var body io.Reader

	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			fmt.Printf("JSON编码失败: %v\n", err)
			return nil
		}
		body = bytes.NewBuffer(jsonData)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return nil
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return nil
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return nil
	}

	fmt.Printf("状态码: %d\n", resp.StatusCode)
	return responseBody
}
