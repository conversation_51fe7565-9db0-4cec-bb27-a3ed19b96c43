package ai

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"
	"github.com/sirupsen/logrus"
)

// parameterInferenceEngine 第二层参数推断器实现
type parameterInferenceEngine struct {
	deepseekService  *service.DeepSeekService
	hostService      workflow.HostServiceInterface
	logger           *logrus.Logger
	scenarioHandlers map[string]ScenarioHandler
	contextManager   *ContextManager
}

// NewParameterInferenceEngine 创建参数推断器
func NewParameterInferenceEngine(
	deepseekService *service.DeepSeekService,
	hostService workflow.HostServiceInterface,
	logger *logrus.Logger,
) ParameterInferenceEngine {
	engine := &parameterInferenceEngine{
		deepseekService:  deepseekService,
		hostService:      hostService,
		logger:           logger,
		scenarioHandlers: make(map[string]ScenarioHandler),
	}

	// 注册场景处理器
	engine.registerBuiltinHandlers()

	return engine
}

// InferParameters 推断参数
func (pie *parameterInferenceEngine) InferParameters(
	ctx context.Context,
	classification *ClassificationResult,
	message string,
	context *ConversationContext,
) (*InferenceResult, error) {
	start := time.Now()

	pie.logger.WithFields(logrus.Fields{
		"intent":     classification.Intent,
		"confidence": classification.Confidence,
		"entities":   len(classification.Entities),
	}).Info("Starting parameter inference")

	// 获取场景处理器
	handler, err := pie.GetScenarioHandler(classification.Intent)
	if err != nil {
		return nil, fmt.Errorf("no handler for intent %s: %w", classification.Intent, err)
	}

	// 检查处理器是否能处理此场景
	if !handler.CanHandle(classification.Intent, classification.Entities) {
		return nil, fmt.Errorf("handler cannot process this scenario")
	}

	// 生成命令序列
	commands, err := handler.GenerateCommands(ctx, classification, message)
	if err != nil {
		return nil, fmt.Errorf("failed to generate commands: %w", err)
	}

	// 生成执行计划
	executionPlan := handler.GenerateExecutionPlan(commands)

	// 构建推断结果
	result := &InferenceResult{
		Parameters:    pie.extractParameters(classification, commands),
		Commands:      commands,
		ExecutionPlan: executionPlan,
		Explanation:   pie.generateExplanation(classification, commands, handler),
		Confidence:    pie.calculateInferenceConfidence(classification, commands),
		Timestamp:     time.Now(),
	}

	pie.logger.WithFields(logrus.Fields{
		"intent":          classification.Intent,
		"commands_count":  len(commands),
		"processing_time": time.Since(start),
		"confidence":      result.Confidence,
	}).Info("Parameter inference completed")

	return result, nil
}

// GetScenarioHandler 获取场景处理器
func (pie *parameterInferenceEngine) GetScenarioHandler(intent string) (ScenarioHandler, error) {
	handler, exists := pie.scenarioHandlers[intent]
	if !exists {
		return nil, fmt.Errorf("no handler registered for intent: %s", intent)
	}
	return handler, nil
}

// RegisterScenarioHandler 注册场景处理器
func (pie *parameterInferenceEngine) RegisterScenarioHandler(intent string, handler ScenarioHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}

	pie.scenarioHandlers[intent] = handler
	pie.logger.WithFields(logrus.Fields{
		"intent":      intent,
		"description": handler.GetDescription(),
	}).Info("Scenario handler registered")

	return nil
}

// registerBuiltinHandlers 注册内置处理器
func (pie *parameterInferenceEngine) registerBuiltinHandlers() {
	// 连接诊断处理器
	pie.RegisterScenarioHandler(IntentConnectionDiagnosis, NewConnectionDiagnosisHandler(pie.deepseekService, pie.hostService, pie.logger))

	// 命令执行处理器
	pie.RegisterScenarioHandler(IntentCommandExecution, NewCommandExecutionHandler(pie.deepseekService, pie.logger))

	// 系统监控处理器
	pie.RegisterScenarioHandler(IntentSystemMonitoring, NewSystemMonitoringHandler(pie.deepseekService, pie.logger))

	// 网络诊断处理器
	pie.RegisterScenarioHandler(IntentNetworkDiagnosis, NewNetworkDiagnosisHandler(pie.deepseekService, pie.logger))

	// 主机管理处理器
	pie.RegisterScenarioHandler(IntentHostManagement, NewHostManagementHandler(pie.deepseekService, pie.hostService, pie.logger))

	// 服务管理处理器
	pie.RegisterScenarioHandler(IntentServiceManagement, NewServiceManagementHandler(pie.deepseekService, pie.logger))

	// 日志分析处理器
	pie.RegisterScenarioHandler(IntentLogAnalysis, NewLogAnalysisHandler(pie.deepseekService, pie.logger))

	// 文件操作处理器
	pie.RegisterScenarioHandler(IntentFileOperations, NewFileOperationsHandler(pie.deepseekService, pie.logger))

	// 安全检查处理器
	pie.RegisterScenarioHandler(IntentSecurityCheck, NewSecurityCheckHandler(pie.deepseekService, pie.logger))

	// 性能分析处理器
	pie.RegisterScenarioHandler(IntentPerformanceAnalysis, NewPerformanceAnalysisHandler(pie.deepseekService, pie.logger))

	// 通用对话处理器
	pie.RegisterScenarioHandler(IntentGeneralChat, NewGeneralChatHandler(pie.deepseekService, pie.logger))
}

// extractParameters 提取参数
func (pie *parameterInferenceEngine) extractParameters(classification *ClassificationResult, commands []CommandSequence) map[string]interface{} {
	parameters := make(map[string]interface{})

	// 从分类结果中提取实体作为参数
	for key, value := range classification.Entities {
		parameters[key] = value
	}

	// 添加意图相关参数
	parameters["intent"] = classification.Intent
	parameters["confidence"] = classification.Confidence

	// 添加命令相关参数
	if len(commands) > 0 {
		parameters["command_count"] = len(commands)
		parameters["first_command"] = commands[0].Command
	}

	// 根据意图类型添加特定参数
	switch classification.Intent {
	case IntentConnectionDiagnosis:
		parameters["diagnosis_type"] = pie.getDiagnosisType(classification)
	case IntentSystemMonitoring:
		parameters["monitoring_type"] = pie.getMonitoringType(classification)
	case IntentNetworkDiagnosis:
		parameters["network_test_type"] = pie.getNetworkTestType(classification)
	}

	return parameters
}

// generateExplanation 生成解释
func (pie *parameterInferenceEngine) generateExplanation(classification *ClassificationResult, commands []CommandSequence, handler ScenarioHandler) string {
	explanation := fmt.Sprintf("基于意图'%s'(置信度: %.2f)，", classification.Intent, classification.Confidence)

	if len(commands) > 0 {
		explanation += fmt.Sprintf("我将执行%d个步骤来完成您的请求：\n", len(commands))
		for i, cmd := range commands {
			explanation += fmt.Sprintf("%d. %s\n", i+1, cmd.Description)
		}
	} else {
		explanation += "我将为您提供相关信息和建议。"
	}

	return explanation
}

// calculateInferenceConfidence 计算推断置信度
func (pie *parameterInferenceEngine) calculateInferenceConfidence(classification *ClassificationResult, commands []CommandSequence) float64 {
	baseConfidence := classification.Confidence

	// 根据命令数量调整置信度
	if len(commands) == 0 {
		return baseConfidence * 0.7 // 没有生成命令，降低置信度
	}

	// 根据实体完整性调整置信度
	entityBonus := 0.0
	if _, hasIP := classification.Entities[EntityIPAddress]; hasIP {
		entityBonus += 0.1
	}
	if _, hasAction := classification.Entities["action"]; hasAction {
		entityBonus += 0.05
	}

	return min(1.0, baseConfidence+entityBonus)
}

// getDiagnosisType 获取诊断类型
func (pie *parameterInferenceEngine) getDiagnosisType(classification *ClassificationResult) string {
	if action, exists := classification.Entities["action"]; exists {
		switch action {
		case "check_login":
			return "ssh_authentication"
		case "check_connection":
			return "connection_test"
		case "check_command_execution":
			return "command_execution_error"
		}
	}
	return "general_diagnosis"
}

// getMonitoringType 获取监控类型
func (pie *parameterInferenceEngine) getMonitoringType(classification *ClassificationResult) string {
	// 根据实体和上下文确定监控类型
	if _, exists := classification.Entities["cpu"]; exists {
		return "cpu_monitoring"
	}
	if _, exists := classification.Entities["memory"]; exists {
		return "memory_monitoring"
	}
	if _, exists := classification.Entities["disk"]; exists {
		return "disk_monitoring"
	}
	return "system_overview"
}

// getNetworkTestType 获取网络测试类型
func (pie *parameterInferenceEngine) getNetworkTestType(classification *ClassificationResult) string {
	if _, hasPort := classification.Entities[EntityPort]; hasPort {
		return "port_connectivity"
	}
	if _, hasIP := classification.Entities[EntityIPAddress]; hasIP {
		return "ping_test"
	}
	return "network_overview"
}

// min 辅助函数
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
