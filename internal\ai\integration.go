package ai

import (
	"context"
	"fmt"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"
	"github.com/sirupsen/logrus"
)

// DualLayerIntegration 双层AI集成器
type DualLayerIntegration struct {
	dualLayerService *DualLayerAIService
	logger           *logrus.Logger
	enabled          bool
}

// NewDualLayerIntegration 创建双层AI集成器
func NewDualLayerIntegration(
	deepseekService *service.DeepSeekService,
	hostService workflow.HostServiceInterface,
	logger *logrus.Logger,
) *DualLayerIntegration {
	dualLayerService := NewDualLayerAIService(deepseekService, hostService, nil, logger)

	return &DualLayerIntegration{
		dualLayerService: dualLayerService,
		logger:           logger,
		enabled:          true,
	}
}

// ProcessMessage 处理消息（兼容现有接口）
func (dli *DualLayerIntegration) ProcessMessage(ctx context.Context, req *service.ProcessMessageRequest) (*service.ProcessMessageResponse, error) {
	if !dli.enabled {
		return nil, fmt.Errorf("dual-layer AI is disabled")
	}

	dli.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("DualLayerIntegration: Processing message")

	return dli.dualLayerService.ProcessMessage(ctx, req)
}

// ExtractIntent 提取意图（兼容现有接口）
func (dli *DualLayerIntegration) ExtractIntent(ctx context.Context, message string, context *service.ConversationContext) (*service.IntentResult, error) {
	if !dli.enabled {
		return nil, fmt.Errorf("dual-layer AI is disabled")
	}

	return dli.dualLayerService.ExtractIntent(ctx, message, context)
}

// IsEnabled 检查是否启用
func (dli *DualLayerIntegration) IsEnabled() bool {
	return dli.enabled
}

// Enable 启用双层AI
func (dli *DualLayerIntegration) Enable() {
	dli.enabled = true
	dli.logger.Info("DualLayerIntegration: Enabled")
}

// Disable 禁用双层AI
func (dli *DualLayerIntegration) Disable() {
	dli.enabled = false
	dli.logger.Info("DualLayerIntegration: Disabled")
}

// GetService 获取双层AI服务
func (dli *DualLayerIntegration) GetService() *DualLayerAIService {
	return dli.dualLayerService
}

// HealthCheck 健康检查
func (dli *DualLayerIntegration) HealthCheck(ctx context.Context) error {
	if !dli.enabled {
		return fmt.Errorf("dual-layer AI is disabled")
	}

	return dli.dualLayerService.HealthCheck(ctx)
}

// GetStatistics 获取统计信息
func (dli *DualLayerIntegration) GetStatistics() map[string]interface{} {
	stats := dli.dualLayerService.GetStatistics()
	stats["enabled"] = dli.enabled
	return stats
}

// ProcessMessageWithDualLayer 使用双层AI处理消息（扩展接口）
func (dli *DualLayerIntegration) ProcessMessageWithDualLayer(ctx context.Context, req *DualLayerRequest) (*DualLayerResponse, error) {
	if !dli.enabled {
		return nil, fmt.Errorf("dual-layer AI is disabled")
	}

	return dli.dualLayerService.ProcessMessageWithDualLayer(ctx, req)
}

// TestConnectionDiagnosis 测试连接诊断功能
func (dli *DualLayerIntegration) TestConnectionDiagnosis(ctx context.Context, targetHost string) (*DualLayerResponse, error) {
	if !dli.enabled {
		return nil, fmt.Errorf("dual-layer AI is disabled")
	}

	testMessage := fmt.Sprintf("检查%s执行命令报什么错误", targetHost)

	req := &DualLayerRequest{
		SessionID: "test_connection_diagnosis",
		UserID:    0,
		Message:   testMessage,
		Context:   dli.dualLayerService.contextManager.GetOrCreateContext("test_connection_diagnosis", 0),
	}

	return dli.dualLayerService.ProcessMessageWithDualLayer(ctx, req)
}

// TestSSHDiagnosis 测试SSH诊断功能
func (dli *DualLayerIntegration) TestSSHDiagnosis(ctx context.Context, targetHost string) (*DualLayerResponse, error) {
	if !dli.enabled {
		return nil, fmt.Errorf("dual-layer AI is disabled")
	}

	testMessage := fmt.Sprintf("检查%s登录报什么错误", targetHost)

	req := &DualLayerRequest{
		SessionID: "test_ssh_diagnosis",
		UserID:    0,
		Message:   testMessage,
		Context:   dli.dualLayerService.contextManager.GetOrCreateContext("test_ssh_diagnosis", 0),
	}

	return dli.dualLayerService.ProcessMessageWithDualLayer(ctx, req)
}

// RegisterCustomHandler 注册自定义场景处理器
func (dli *DualLayerIntegration) RegisterCustomHandler(intent string, handler ScenarioHandler) error {
	return dli.dualLayerService.RegisterScenarioHandler(intent, handler)
}

// GetSupportedIntents 获取支持的意图类型
func (dli *DualLayerIntegration) GetSupportedIntents() []string {
	return dli.dualLayerService.GetSupportedIntents()
}

// UpdateConfig 更新配置
func (dli *DualLayerIntegration) UpdateConfig(config *DualLayerConfig) {
	dli.dualLayerService.UpdateConfig(config)
}

// GetProcessingStatus 获取处理状态
func (dli *DualLayerIntegration) GetProcessingStatus(sessionID string) (*ProcessingStatus, error) {
	return dli.dualLayerService.GetProcessingStatus(sessionID)
}

// CancelProcessing 取消处理
func (dli *DualLayerIntegration) CancelProcessing(sessionID string) error {
	return dli.dualLayerService.CancelProcessing(sessionID)
}

// CreateTestScenarios 创建测试场景
func (dli *DualLayerIntegration) CreateTestScenarios() []TestScenario {
	return []TestScenario{
		{
			Name:           "连接诊断测试",
			Description:    "测试主机连接诊断功能",
			Input:          "检查**************执行命令报什么错误",
			ExpectedIntent: IntentConnectionDiagnosis,
			ExpectedCommands: []string{
				"ping -c 2 **************",
				"nc -zv ************** 22",
				"ssh -o ConnectTimeout=5 ************** 'uptime'",
			},
		},
		{
			Name:           "SSH诊断测试",
			Description:    "测试SSH登录诊断功能",
			Input:          "检查**************登录报什么错误",
			ExpectedIntent: IntentConnectionDiagnosis,
			ExpectedCommands: []string{
				"nc -zv ************** 22",
				"ssh -o ConnectTimeout=5 -o BatchMode=yes -o PasswordAuthentication=no ************** 'echo ssh_key_test'",
			},
		},
		{
			Name:           "系统监控测试",
			Description:    "测试系统监控功能",
			Input:          "查看系统状态",
			ExpectedIntent: IntentSystemMonitoring,
			ExpectedCommands: []string{
				"top -bn1 | head -20",
				"df -h",
				"free -h",
			},
		},
		{
			Name:           "网络诊断测试",
			Description:    "测试网络诊断功能",
			Input:          "测试网络连通性",
			ExpectedIntent: IntentNetworkDiagnosis,
			ExpectedCommands: []string{
				"ping -c 4 localhost",
				"netstat -tuln",
			},
		},
	}
}

// TestScenario 测试场景
type TestScenario struct {
	Name             string   `json:"name"`
	Description      string   `json:"description"`
	Input            string   `json:"input"`
	ExpectedIntent   string   `json:"expected_intent"`
	ExpectedCommands []string `json:"expected_commands"`
}

// RunTestScenario 运行测试场景
func (dli *DualLayerIntegration) RunTestScenario(ctx context.Context, scenario TestScenario) (*TestResult, error) {
	if !dli.enabled {
		return nil, fmt.Errorf("dual-layer AI is disabled")
	}

	req := &DualLayerRequest{
		SessionID: fmt.Sprintf("test_%s", scenario.Name),
		UserID:    0,
		Message:   scenario.Input,
		Context:   dli.dualLayerService.contextManager.GetOrCreateContext(fmt.Sprintf("test_%s", scenario.Name), 0),
	}

	response, err := dli.dualLayerService.ProcessMessageWithDualLayer(ctx, req)
	if err != nil {
		return &TestResult{
			Scenario: scenario,
			Success:  false,
			Error:    err.Error(),
		}, nil
	}

	// 验证结果
	success := response.Intent == scenario.ExpectedIntent

	return &TestResult{
		Scenario:     scenario,
		Success:      success,
		ActualIntent: response.Intent,
		Confidence:   response.Confidence,
		CommandCount: len(response.Commands),
		Response:     response.Content,
	}, nil
}

// TestResult 测试结果
type TestResult struct {
	Scenario     TestScenario `json:"scenario"`
	Success      bool         `json:"success"`
	ActualIntent string       `json:"actual_intent"`
	Confidence   float64      `json:"confidence"`
	CommandCount int          `json:"command_count"`
	Response     string       `json:"response"`
	Error        string       `json:"error,omitempty"`
}
