package ai

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

// DualLayerAIService 双层AI服务
type DualLayerAIService struct {
	dispatcher     UnifiedDispatcher
	contextManager *ContextManager
	logger         *logrus.Logger
	config         *DualLayerConfig

	// 降级服务
	fallbackService interface{}
}

// DualLayerConfig 双层AI配置
type DualLayerConfig struct {
	EnableFallback       bool          `json:"enable_fallback"`
	FallbackThreshold    float64       `json:"fallback_threshold"`
	MaxProcessingTime    time.Duration `json:"max_processing_time"`
	EnableAsyncExecution bool          `json:"enable_async_execution"`
}

// NewDualLayerAIService 创建双层AI服务
func NewDualLayerAIService(
	deepseekService *service.DeepSeekService,
	hostService workflow.HostServiceInterface,
	fallbackService interface{}, // 暂时使用interface{}
	logger *logrus.Logger,
) *DualLayerAIService {
	// 创建上下文管理器
	contextManager := NewContextManager(logger, DefaultContextConfig())

	// 创建第一层：意图分类器
	classifier := NewIntentClassifier(deepseekService, contextManager, logger)

	// 创建第二层：参数推断器
	inferenceEngine := NewParameterInferenceEngine(deepseekService, hostService, logger)

	// 创建执行引擎
	executionEngine := NewExecutionEngine(hostService, logger)

	// 创建响应构建器
	responseBuilder := NewResponseBuilder(logger)

	// 创建统一调度器
	dispatcher := NewUnifiedDispatcher(classifier, inferenceEngine, executionEngine, responseBuilder, logger)

	config := &DualLayerConfig{
		EnableFallback:       true,
		FallbackThreshold:    0.6,
		MaxProcessingTime:    5 * time.Minute,
		EnableAsyncExecution: true,
	}

	return &DualLayerAIService{
		dispatcher:      dispatcher,
		contextManager:  contextManager,
		logger:          logger,
		config:          config,
		fallbackService: fallbackService,
	}
}

// ProcessMessage 处理消息（实现AIServiceInterface）
func (dlas *DualLayerAIService) ProcessMessage(ctx context.Context, req *service.ProcessMessageRequest) (*service.ProcessMessageResponse, error) {
	start := time.Now()

	dlas.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("DualLayerAIService: Processing message")

	// 转换请求格式
	dualLayerReq := &DualLayerRequest{
		SessionID: req.SessionID,
		UserID:    req.UserID,
		Message:   req.Message,
		Context:   dlas.contextManager.GetOrCreateContext(req.SessionID, req.UserID),
	}

	// 使用双层AI处理
	response, err := dlas.dispatcher.ProcessMessage(ctx, dualLayerReq)
	if err != nil {
		dlas.logger.WithError(err).Error("DualLayerAIService: Dual-layer processing failed")

		// 如果启用降级，使用原有服务
		if dlas.config.EnableFallback && dlas.fallbackService != nil {
			dlas.logger.Info("DualLayerAIService: Falling back to original AI service")
			// TODO: 实现降级逻辑
		}

		return nil, fmt.Errorf("dual-layer AI processing failed: %w", err)
	}

	// 转换响应格式
	result := &service.ProcessMessageResponse{
		Content:        response.Content,
		Intent:         response.Intent,
		Confidence:     response.Confidence,
		Parameters:     response.Parameters,
		TokenCount:     50, // 估算
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}

	dlas.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          response.Intent,
		"confidence":      response.Confidence,
		"requires_action": response.RequiresAction,
		"processing_time": result.ProcessingTime,
	}).Info("DualLayerAIService: Message processing completed")

	return result, nil
}

// ExtractIntent 提取意图（实现AIServiceInterface）
func (dlas *DualLayerAIService) ExtractIntent(ctx context.Context, message string, context *service.ConversationContext) (*service.IntentResult, error) {
	dlas.logger.WithFields(logrus.Fields{
		"message": message,
	}).Info("DualLayerAIService: Extracting intent")

	// 获取或创建上下文
	var aiContext *ConversationContext
	if context != nil {
		aiContext = dlas.contextManager.GetOrCreateContext(context.SessionID, context.UserID)
	}

	// 使用第一层分类器提取意图
	classifier := dlas.dispatcher.(*unifiedDispatcher).classifier
	classification, err := classifier.ClassifyIntent(ctx, message, aiContext)
	if err != nil {
		// 降级到原有服务
		if dlas.config.EnableFallback && dlas.fallbackService != nil {
			// TODO: 实现降级逻辑
		}
		return nil, fmt.Errorf("intent classification failed: %w", err)
	}

	// 转换结果格式
	result := &service.IntentResult{
		Type:       classification.Intent,
		Confidence: classification.Confidence,
		Parameters: classification.Entities,
		Command:    "", // 双层系统中命令在第二层生成
	}

	dlas.logger.WithFields(logrus.Fields{
		"intent":     result.Type,
		"confidence": result.Confidence,
	}).Info("DualLayerAIService: Intent extraction completed")

	return result, nil
}

// ProcessMessageWithDualLayer 使用双层AI处理消息（扩展接口）
func (dlas *DualLayerAIService) ProcessMessageWithDualLayer(ctx context.Context, req *DualLayerRequest) (*DualLayerResponse, error) {
	return dlas.dispatcher.ProcessMessage(ctx, req)
}

// GetProcessingStatus 获取处理状态
func (dlas *DualLayerAIService) GetProcessingStatus(sessionID string) (*ProcessingStatus, error) {
	return dlas.dispatcher.GetProcessingStatus(sessionID)
}

// CancelProcessing 取消处理
func (dlas *DualLayerAIService) CancelProcessing(sessionID string) error {
	return dlas.dispatcher.CancelProcessing(sessionID)
}

// GetSupportedIntents 获取支持的意图类型
func (dlas *DualLayerAIService) GetSupportedIntents() []string {
	classifier := dlas.dispatcher.(*unifiedDispatcher).classifier
	return classifier.GetSupportedIntents()
}

// RegisterScenarioHandler 注册场景处理器
func (dlas *DualLayerAIService) RegisterScenarioHandler(intent string, handler ScenarioHandler) error {
	inferenceEngine := dlas.dispatcher.(*unifiedDispatcher).inferenceEngine
	return inferenceEngine.RegisterScenarioHandler(intent, handler)
}

// UpdateConfig 更新配置
func (dlas *DualLayerAIService) UpdateConfig(config *DualLayerConfig) {
	dlas.config = config
	dlas.logger.WithFields(logrus.Fields{
		"enable_fallback":        config.EnableFallback,
		"fallback_threshold":     config.FallbackThreshold,
		"max_processing_time":    config.MaxProcessingTime,
		"enable_async_execution": config.EnableAsyncExecution,
	}).Info("DualLayerAIService: Configuration updated")
}

// GetStatistics 获取统计信息
func (dlas *DualLayerAIService) GetStatistics() map[string]interface{} {
	return map[string]interface{}{
		"supported_intents": len(dlas.GetSupportedIntents()),
		"fallback_enabled":  dlas.config.EnableFallback,
		"async_enabled":     dlas.config.EnableAsyncExecution,
	}
}

// HealthCheck 健康检查
func (dlas *DualLayerAIService) HealthCheck(ctx context.Context) error {
	// 测试双层AI系统的基本功能
	testReq := &DualLayerRequest{
		SessionID: "health_check",
		UserID:    0,
		Message:   "健康检查",
		Context:   dlas.contextManager.GetOrCreateContext("health_check", 0),
	}

	_, err := dlas.dispatcher.ProcessMessage(ctx, testReq)
	if err != nil {
		return fmt.Errorf("dual-layer AI health check failed: %w", err)
	}

	return nil
}

// 实现其他AIServiceInterface方法的存根
func (dlas *DualLayerAIService) GetAvailableTools(userID int64) ([]service.ToolDefinition, error) {
	// TODO: 实现工具获取
	return []service.ToolDefinition{}, nil
}

func (dlas *DualLayerAIService) ExecuteTool(ctx context.Context, toolCall *service.ToolCall, context *service.ConversationContext) (*service.ToolResult, error) {
	// TODO: 实现工具执行
	return nil, fmt.Errorf("tool execution not implemented in dual-layer service")
}

func (dlas *DualLayerAIService) GenerateResponse(ctx context.Context, req *service.GenerateResponseRequest) (*service.GenerateResponseResult, error) {
	// TODO: 实现响应生成
	return nil, fmt.Errorf("response generation not implemented in dual-layer service")
}

func (dlas *DualLayerAIService) SummarizeConversation(ctx context.Context, sessionID string) (*service.ConversationSummary, error) {
	// TODO: 实现对话总结
	return nil, fmt.Errorf("conversation summarization not implemented in dual-layer service")
}

func (dlas *DualLayerAIService) ValidateCommand(ctx context.Context, command string, context *service.ConversationContext) (*service.CommandValidation, error) {
	// TODO: 实现命令验证
	return nil, fmt.Errorf("command validation not implemented in dual-layer service")
}
