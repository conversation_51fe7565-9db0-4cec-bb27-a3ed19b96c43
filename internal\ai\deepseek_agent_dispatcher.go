package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/agent"
	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
)

// DeepSeekAgentDispatcher 基于DeepSeek AI的智能Agent调度器
type DeepSeekAgentDispatcher struct {
	deepseekService *service.DeepSeekService
	agentRegistry   *agent.AgentRegistry
	logger          *logrus.Logger
	config          *DispatcherConfig
}

// DispatcherConfig 调度器配置
type DispatcherConfig struct {
	MaxAgentsPerTask     int     `json:"max_agents_per_task"`
	ConfidenceThreshold  float64 `json:"confidence_threshold"`
	EnableMultiAgent     bool    `json:"enable_multi_agent"`
	TimeoutSeconds       int     `json:"timeout_seconds"`
	EnableParameterCheck bool    `json:"enable_parameter_check"`
}

// AgentDispatchRequest Agent调度请求
type AgentDispatchRequest struct {
	UserMessage   string                 `json:"user_message"`
	SessionID     string                 `json:"session_id"`
	UserID        int64                  `json:"user_id"`
	Context       map[string]interface{} `json:"context"`
	AvailableData map[string]interface{} `json:"available_data"`
}

// AgentDispatchResult Agent调度结果
type AgentDispatchResult struct {
	SelectedAgents   []*SelectedAgent       `json:"selected_agents"`
	ExecutionPlan    *ExecutionPlan         `json:"execution_plan"`
	Confidence       float64                `json:"confidence"`
	Reasoning        string                 `json:"reasoning"`
	EstimatedTime    time.Duration          `json:"estimated_time"`
	RequiredData     []string               `json:"required_data"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// SelectedAgent 选中的Agent
type SelectedAgent struct {
	AgentID     string                 `json:"agent_id"`
	AgentName   string                 `json:"agent_name"`
	Capability  string                 `json:"capability"`
	Parameters  map[string]interface{} `json:"parameters"`
	Priority    int                    `json:"priority"`
	Confidence  float64                `json:"confidence"`
	Description string                 `json:"description"`
}

// ExecutionPlan 执行计划
type ExecutionPlan struct {
	Strategy     string                 `json:"strategy"`      // sequential, parallel, conditional, pipeline
	Steps        []*ExecutionStep       `json:"steps"`
	Dependencies map[string][]string    `json:"dependencies"`
	Timeout      time.Duration          `json:"timeout"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ExecutionStep 执行步骤
type ExecutionStep struct {
	StepID      string                 `json:"step_id"`
	AgentID     string                 `json:"agent_id"`
	Capability  string                 `json:"capability"`
	Parameters  map[string]interface{} `json:"parameters"`
	Condition   string                 `json:"condition"`
	OnSuccess   string                 `json:"on_success"`
	OnFailure   string                 `json:"on_failure"`
	Timeout     time.Duration          `json:"timeout"`
	Description string                 `json:"description"`
}

// NewDeepSeekAgentDispatcher 创建DeepSeek Agent调度器
func NewDeepSeekAgentDispatcher(
	deepseekService *service.DeepSeekService,
	agentRegistry *agent.AgentRegistry,
	logger *logrus.Logger,
) *DeepSeekAgentDispatcher {
	config := &DispatcherConfig{
		MaxAgentsPerTask:     5,
		ConfidenceThreshold:  0.7,
		EnableMultiAgent:     true,
		TimeoutSeconds:       30,
		EnableParameterCheck: true,
	}

	return &DeepSeekAgentDispatcher{
		deepseekService: deepseekService,
		agentRegistry:   agentRegistry,
		logger:          logger,
		config:          config,
	}
}

// DispatchAgents 智能调度Agent
func (dad *DeepSeekAgentDispatcher) DispatchAgents(ctx context.Context, request *AgentDispatchRequest) (*AgentDispatchResult, error) {
	start := time.Now()

	dad.logger.WithFields(logrus.Fields{
		"user_message": request.UserMessage,
		"session_id":   request.SessionID,
		"user_id":      request.UserID,
	}).Info("Starting DeepSeek agent dispatch")

	// 1. 获取所有可用Agent的能力描述
	agentCapabilities, err := dad.buildAgentCapabilitiesDescription()
	if err != nil {
		return nil, fmt.Errorf("failed to build agent capabilities: %w", err)
	}

	// 2. 构建DeepSeek提示词
	systemPrompt := dad.buildDispatchSystemPrompt(agentCapabilities)
	userPrompt := dad.buildDispatchUserPrompt(request)

	// 3. 调用DeepSeek API进行智能分析
	messages := []service.Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := dad.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to call DeepSeek API: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from DeepSeek")
	}

	// 4. 解析DeepSeek响应
	result, err := dad.parseDispatchResponse(response.Choices[0].Message.Content)
	if err != nil {
		dad.logger.WithError(err).Warn("Failed to parse DeepSeek response, using fallback")
		return dad.createFallbackResult(request), nil
	}

	// 5. 验证和优化调度结果
	if err := dad.validateDispatchResult(result); err != nil {
		dad.logger.WithError(err).Warn("Dispatch result validation failed, applying corrections")
		dad.correctDispatchResult(result)
	}

	// 6. 记录调度结果
	dad.logger.WithFields(logrus.Fields{
		"selected_agents": len(result.SelectedAgents),
		"confidence":      result.Confidence,
		"strategy":        result.ExecutionPlan.Strategy,
		"processing_time": time.Since(start),
	}).Info("Agent dispatch completed")

	return result, nil
}

// buildAgentCapabilitiesDescription 构建Agent能力描述
func (dad *DeepSeekAgentDispatcher) buildAgentCapabilitiesDescription() (string, error) {
	agents := dad.agentRegistry.GetAllAgents()
	if len(agents) == 0 {
		return "", fmt.Errorf("no agents available")
	}

	var capabilities strings.Builder
	capabilities.WriteString("=== 可用Agent及其能力 ===\n\n")

	for agentID, registration := range agents {
		capabilities.WriteString(fmt.Sprintf("## Agent: %s\n", registration.Metadata.Name))
		capabilities.WriteString(fmt.Sprintf("- ID: %s\n", agentID))
		capabilities.WriteString(fmt.Sprintf("- 类别: %s\n", registration.Metadata.Category))
		capabilities.WriteString(fmt.Sprintf("- 描述: %s\n", registration.Metadata.Description))
		capabilities.WriteString(fmt.Sprintf("- 状态: %s\n", registration.Status))
		
		capabilities.WriteString("- 能力列表:\n")
		for _, capability := range registration.Capabilities {
			capabilities.WriteString(fmt.Sprintf("  * %s: %s\n", capability.Name, capability.Description))
			
			if len(capability.Parameters) > 0 {
				capabilities.WriteString("    参数:\n")
				for _, param := range capability.Parameters {
					required := "可选"
					if param.Required {
						required = "必需"
					}
					capabilities.WriteString(fmt.Sprintf("      - %s (%s): %s\n", param.Name, required, param.Description))
				}
			}
		}
		capabilities.WriteString("\n")
	}

	return capabilities.String(), nil
}

// buildDispatchSystemPrompt 构建调度系统提示词
func (dad *DeepSeekAgentDispatcher) buildDispatchSystemPrompt(agentCapabilities string) string {
	return fmt.Sprintf(`你是一个专业的AI运维Agent调度专家。你的任务是根据用户需求，智能选择最合适的Agent组合来完成任务。

%s

## 调度规则

### 1. Agent选择原则
- 根据用户需求选择最匹配的Agent
- 优先选择专业性强、能力匹配度高的Agent
- 考虑Agent的当前状态和可用性
- 支持多Agent协作完成复杂任务

### 2. 参数推理原则
- 从用户输入中智能提取Agent所需参数
- 对缺失的必需参数进行合理推断或标记
- 验证参数的有效性和完整性

### 3. 执行策略选择
- sequential: 顺序执行，适用于有依赖关系的任务
- parallel: 并行执行，适用于独立的任务
- conditional: 条件执行，根据条件选择不同路径
- pipeline: 管道执行，前一个Agent的输出作为后一个的输入

### 4. 响应格式
请严格按照以下JSON格式返回：

{
  "selected_agents": [
    {
      "agent_id": "agent_id",
      "agent_name": "agent_name", 
      "capability": "capability_name",
      "parameters": {
        "param1": "value1",
        "param2": "value2"
      },
      "priority": 1,
      "confidence": 0.95,
      "description": "执行描述"
    }
  ],
  "execution_plan": {
    "strategy": "sequential",
    "steps": [
      {
        "step_id": "step_1",
        "agent_id": "agent_id",
        "capability": "capability_name",
        "parameters": {},
        "condition": "",
        "on_success": "step_2",
        "on_failure": "end",
        "timeout": "30s",
        "description": "步骤描述"
      }
    ],
    "dependencies": {},
    "timeout": "60s"
  },
  "confidence": 0.95,
  "reasoning": "选择理由和执行计划说明",
  "estimated_time": "30s",
  "required_data": ["需要的额外数据"]
}

## 重要提示
- 必须返回有效的JSON格式
- 所有Agent ID和能力名称必须与上述列表完全匹配
- 参数值必须符合Agent的参数要求
- 置信度应该反映选择的准确性`, agentCapabilities)
}

// buildDispatchUserPrompt 构建用户提示词
func (dad *DeepSeekAgentDispatcher) buildDispatchUserPrompt(request *AgentDispatchRequest) string {
	prompt := fmt.Sprintf("用户需求：%s", request.UserMessage)

	if len(request.Context) > 0 {
		contextJSON, _ := json.Marshal(request.Context)
		prompt += fmt.Sprintf("\n\n上下文信息：%s", string(contextJSON))
	}

	if len(request.AvailableData) > 0 {
		dataJSON, _ := json.Marshal(request.AvailableData)
		prompt += fmt.Sprintf("\n\n可用数据：%s", string(dataJSON))
	}

	prompt += "\n\n请根据用户需求，智能选择合适的Agent组合并生成执行计划。"

	return prompt
}

// parseDispatchResponse 解析调度响应
func (dad *DeepSeekAgentDispatcher) parseDispatchResponse(content string) (*AgentDispatchResult, error) {
	// 尝试提取JSON内容
	jsonStart := strings.Index(content, "{")
	jsonEnd := strings.LastIndex(content, "}")
	
	if jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonContent := content[jsonStart : jsonEnd+1]

	var result AgentDispatchResult
	if err := json.Unmarshal([]byte(jsonContent), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 解析时间字符串
	if result.EstimatedTime == 0 {
		result.EstimatedTime = 30 * time.Second // 默认值
	}

	return &result, nil
}

// validateDispatchResult 验证调度结果
func (dad *DeepSeekAgentDispatcher) validateDispatchResult(result *AgentDispatchResult) error {
	if len(result.SelectedAgents) == 0 {
		return fmt.Errorf("no agents selected")
	}

	if len(result.SelectedAgents) > dad.config.MaxAgentsPerTask {
		return fmt.Errorf("too many agents selected: %d > %d", len(result.SelectedAgents), dad.config.MaxAgentsPerTask)
	}

	if result.Confidence < dad.config.ConfidenceThreshold {
		return fmt.Errorf("confidence too low: %f < %f", result.Confidence, dad.config.ConfidenceThreshold)
	}

	// 验证Agent ID是否存在
	for _, selectedAgent := range result.SelectedAgents {
		if !dad.agentRegistry.IsAgentRegistered(selectedAgent.AgentID) {
			return fmt.Errorf("agent not found: %s", selectedAgent.AgentID)
		}
	}

	return nil
}

// correctDispatchResult 修正调度结果
func (dad *DeepSeekAgentDispatcher) correctDispatchResult(result *AgentDispatchResult) {
	// 移除不存在的Agent
	validAgents := make([]*SelectedAgent, 0)
	for _, selectedAgent := range result.SelectedAgents {
		if dad.agentRegistry.IsAgentRegistered(selectedAgent.AgentID) {
			validAgents = append(validAgents, selectedAgent)
		}
	}
	result.SelectedAgents = validAgents

	// 调整置信度
	if result.Confidence < dad.config.ConfidenceThreshold {
		result.Confidence = dad.config.ConfidenceThreshold
	}

	// 设置默认执行计划
	if result.ExecutionPlan == nil {
		result.ExecutionPlan = &ExecutionPlan{
			Strategy: "sequential",
			Timeout:  60 * time.Second,
		}
	}
}

// createFallbackResult 创建降级结果
func (dad *DeepSeekAgentDispatcher) createFallbackResult(request *AgentDispatchRequest) *AgentDispatchResult {
	return &AgentDispatchResult{
		SelectedAgents: []*SelectedAgent{},
		ExecutionPlan: &ExecutionPlan{
			Strategy: "sequential",
			Timeout:  60 * time.Second,
		},
		Confidence:    0.5,
		Reasoning:     "DeepSeek响应解析失败，使用降级策略",
		EstimatedTime: 30 * time.Second,
		RequiredData:  []string{},
		Metadata: map[string]interface{}{
			"fallback": true,
			"reason":   "parse_error",
		},
	}
}
