package ai

import (
	"encoding/json"
	"fmt"
	"strings"

	"aiops-platform/internal/agent"
	"github.com/sirupsen/logrus"
)

// AgentCapabilityDescriptor Agent能力描述器
type AgentCapabilityDescriptor struct {
	logger *logrus.Logger
}

// CapabilityDescription Agent能力描述
type CapabilityDescription struct {
	AgentID      string                    `json:"agent_id"`
	AgentName    string                    `json:"agent_name"`
	Category     string                    `json:"category"`
	Description  string                    `json:"description"`
	Version      string                    `json:"version"`
	Status       string                    `json:"status"`
	Capabilities []DetailedCapability      `json:"capabilities"`
	Examples     []CapabilityExample       `json:"examples"`
	Constraints  []CapabilityConstraint    `json:"constraints"`
	Metadata     map[string]interface{}    `json:"metadata"`
}

// DetailedCapability 详细能力描述
type DetailedCapability struct {
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Category     string                 `json:"category"`
	Parameters   []ParameterDescription `json:"parameters"`
	ReturnType   string                 `json:"return_type"`
	Examples     []string               `json:"examples"`
	Complexity   string                 `json:"complexity"`    // low, medium, high
	EstimatedTime string                `json:"estimated_time"` // 预估执行时间
	Dependencies []string               `json:"dependencies"`   // 依赖的其他能力
}

// ParameterDescription 参数描述
type ParameterDescription struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	Required     bool        `json:"required"`
	Description  string      `json:"description"`
	DefaultValue interface{} `json:"default_value,omitempty"`
	Validation   string      `json:"validation,omitempty"`
	Examples     []string    `json:"examples,omitempty"`
}

// CapabilityExample 能力使用示例
type CapabilityExample struct {
	UserInput    string                 `json:"user_input"`
	Capability   string                 `json:"capability"`
	Parameters   map[string]interface{} `json:"parameters"`
	Description  string                 `json:"description"`
	ExpectedTime string                 `json:"expected_time"`
}

// CapabilityConstraint 能力约束
type CapabilityConstraint struct {
	Type        string `json:"type"`        // resource, permission, dependency
	Description string `json:"description"`
	Severity    string `json:"severity"`    // low, medium, high, critical
}

// NewAgentCapabilityDescriptor 创建Agent能力描述器
func NewAgentCapabilityDescriptor(logger *logrus.Logger) *AgentCapabilityDescriptor {
	return &AgentCapabilityDescriptor{
		logger: logger,
	}
}

// DescribeAgent 描述Agent能力
func (acd *AgentCapabilityDescriptor) DescribeAgent(agentRegistration *agent.AgentRegistration, agentInstance agent.Agent) *CapabilityDescription {
	metadata := agentRegistration.Metadata
	
	description := &CapabilityDescription{
		AgentID:     metadata.ID,
		AgentName:   metadata.Name,
		Category:    metadata.Category,
		Description: metadata.Description,
		Version:     metadata.Version,
		Status:      string(agentRegistration.Status),
		Metadata:    make(map[string]interface{}),
	}

	// 转换能力描述
	for _, capability := range agentRegistration.Capabilities {
		detailedCap := acd.convertToDetailedCapability(capability)
		description.Capabilities = append(description.Capabilities, detailedCap)
	}

	// 生成使用示例
	description.Examples = acd.generateCapabilityExamples(description)

	// 分析约束条件
	description.Constraints = acd.analyzeConstraints(agentRegistration)

	// 添加元数据
	description.Metadata["registered_at"] = agentRegistration.RegisteredAt
	description.Metadata["last_seen"] = agentRegistration.LastSeen
	description.Metadata["health_status"] = agentInstance.HealthCheck(nil)

	return description
}

// convertToDetailedCapability 转换为详细能力描述
func (acd *AgentCapabilityDescriptor) convertToDetailedCapability(capability agent.Capability) DetailedCapability {
	detailed := DetailedCapability{
		Name:        capability.Name,
		Description: capability.Description,
		Category:    capability.Category,
		ReturnType:  "ExecutionResult",
		Complexity:  acd.assessComplexity(capability),
		EstimatedTime: acd.estimateExecutionTime(capability),
	}

	// 转换参数描述
	for _, param := range capability.Parameters {
		paramDesc := ParameterDescription{
			Name:        param.Name,
			Type:        param.Type,
			Required:    param.Required,
			Description: param.Description,
			Validation:  param.Validation,
		}

		if param.DefaultValue != nil {
			paramDesc.DefaultValue = param.DefaultValue
		}

		// 生成参数示例
		paramDesc.Examples = acd.generateParameterExamples(param)

		detailed.Parameters = append(detailed.Parameters, paramDesc)
	}

	// 生成能力使用示例
	detailed.Examples = acd.generateCapabilityUsageExamples(capability)

	return detailed
}

// assessComplexity 评估能力复杂度
func (acd *AgentCapabilityDescriptor) assessComplexity(capability agent.Capability) string {
	paramCount := len(capability.Parameters)
	
	if paramCount <= 2 {
		return "low"
	} else if paramCount <= 5 {
		return "medium"
	} else {
		return "high"
	}
}

// estimateExecutionTime 估算执行时间
func (acd *AgentCapabilityDescriptor) estimateExecutionTime(capability agent.Capability) string {
	// 根据能力类型估算执行时间
	switch {
	case strings.Contains(strings.ToLower(capability.Name), "list"):
		return "1-3秒"
	case strings.Contains(strings.ToLower(capability.Name), "test"):
		return "5-15秒"
	case strings.Contains(strings.ToLower(capability.Name), "execute"):
		return "10-30秒"
	case strings.Contains(strings.ToLower(capability.Name), "analyze"):
		return "15-60秒"
	case strings.Contains(strings.ToLower(capability.Name), "backup"):
		return "1-10分钟"
	default:
		return "5-30秒"
	}
}

// generateParameterExamples 生成参数示例
func (acd *AgentCapabilityDescriptor) generateParameterExamples(param agent.Parameter) []string {
	examples := []string{}

	switch param.Type {
	case "string":
		if strings.Contains(strings.ToLower(param.Name), "ip") {
			examples = append(examples, "*************", "********")
		} else if strings.Contains(strings.ToLower(param.Name), "host") {
			examples = append(examples, "web-server-01", "db-server-02")
		} else if strings.Contains(strings.ToLower(param.Name), "command") {
			examples = append(examples, "ls -la", "ps aux", "df -h")
		} else {
			examples = append(examples, "example_value")
		}
	case "int", "integer":
		examples = append(examples, "80", "443", "22")
	case "bool", "boolean":
		examples = append(examples, "true", "false")
	case "array":
		examples = append(examples, "[\"item1\", \"item2\"]")
	default:
		examples = append(examples, "example_value")
	}

	return examples
}

// generateCapabilityUsageExamples 生成能力使用示例
func (acd *AgentCapabilityDescriptor) generateCapabilityUsageExamples(capability agent.Capability) []string {
	examples := []string{}

	switch capability.Name {
	case "add_host":
		examples = append(examples, "添加主机 ************* root password123")
	case "test_connection":
		examples = append(examples, "测试主机 ************* 连接")
	case "execute_command":
		examples = append(examples, "在主机 ************* 执行 ls -la")
	case "check_system_status":
		examples = append(examples, "检查系统状态")
	case "analyze_logs":
		examples = append(examples, "分析 /var/log/syslog 日志")
	default:
		examples = append(examples, fmt.Sprintf("使用 %s 功能", capability.Name))
	}

	return examples
}

// generateCapabilityExamples 生成能力示例
func (acd *AgentCapabilityDescriptor) generateCapabilityExamples(description *CapabilityDescription) []CapabilityExample {
	examples := []CapabilityExample{}

	for _, capability := range description.Capabilities {
		example := CapabilityExample{
			Capability:   capability.Name,
			Description:  fmt.Sprintf("使用%s的%s功能", description.AgentName, capability.Name),
			ExpectedTime: capability.EstimatedTime,
			Parameters:   make(map[string]interface{}),
		}

		// 生成示例参数
		for _, param := range capability.Parameters {
			if param.Required && len(param.Examples) > 0 {
				example.Parameters[param.Name] = param.Examples[0]
			}
		}

		// 生成用户输入示例
		switch capability.Name {
		case "add_host":
			example.UserInput = "添加主机 ************* 用户名 root 密码 password123"
		case "test_connection":
			example.UserInput = "测试主机 ************* 的连接状态"
		case "execute_command":
			example.UserInput = "在主机 ************* 上执行 ls -la 命令"
		case "check_system_status":
			example.UserInput = "检查系统状态"
		case "analyze_logs":
			example.UserInput = "分析系统日志文件"
		default:
			example.UserInput = fmt.Sprintf("使用 %s 功能", capability.Name)
		}

		examples = append(examples, example)
	}

	return examples
}

// analyzeConstraints 分析约束条件
func (acd *AgentCapabilityDescriptor) analyzeConstraints(registration *agent.AgentRegistration) []CapabilityConstraint {
	constraints := []CapabilityConstraint{}

	// 检查Agent状态约束
	if registration.Status != agent.StatusHealthy {
		constraints = append(constraints, CapabilityConstraint{
			Type:        "availability",
			Description: fmt.Sprintf("Agent状态异常: %s", registration.Status),
			Severity:    "high",
		})
	}

	// 检查执行条件约束
	for _, condition := range registration.Conditions {
		constraints = append(constraints, CapabilityConstraint{
			Type:        "condition",
			Description: condition.Description,
			Severity:    acd.assessConditionSeverity(condition),
		})
	}

	return constraints
}

// assessConditionSeverity 评估条件严重性
func (acd *AgentCapabilityDescriptor) assessConditionSeverity(condition agent.ExecutionCondition) string {
	if condition.Required {
		return "high"
	}
	return "medium"
}

// GenerateMarkdownDescription 生成Markdown格式的能力描述
func (acd *AgentCapabilityDescriptor) GenerateMarkdownDescription(description *CapabilityDescription) string {
	var md strings.Builder

	md.WriteString(fmt.Sprintf("# %s\n\n", description.AgentName))
	md.WriteString(fmt.Sprintf("**ID**: %s  \n", description.AgentID))
	md.WriteString(fmt.Sprintf("**类别**: %s  \n", description.Category))
	md.WriteString(fmt.Sprintf("**版本**: %s  \n", description.Version))
	md.WriteString(fmt.Sprintf("**状态**: %s  \n\n", description.Status))
	md.WriteString(fmt.Sprintf("**描述**: %s\n\n", description.Description))

	md.WriteString("## 能力列表\n\n")
	for _, capability := range description.Capabilities {
		md.WriteString(fmt.Sprintf("### %s\n\n", capability.Name))
		md.WriteString(fmt.Sprintf("**描述**: %s  \n", capability.Description))
		md.WriteString(fmt.Sprintf("**复杂度**: %s  \n", capability.Complexity))
		md.WriteString(fmt.Sprintf("**预估时间**: %s  \n\n", capability.EstimatedTime))

		if len(capability.Parameters) > 0 {
			md.WriteString("**参数**:\n\n")
			for _, param := range capability.Parameters {
				required := "可选"
				if param.Required {
					required = "必需"
				}
				md.WriteString(fmt.Sprintf("- `%s` (%s, %s): %s\n", param.Name, param.Type, required, param.Description))
			}
			md.WriteString("\n")
		}

		if len(capability.Examples) > 0 {
			md.WriteString("**使用示例**:\n")
			for _, example := range capability.Examples {
				md.WriteString(fmt.Sprintf("- %s\n", example))
			}
			md.WriteString("\n")
		}
	}

	if len(description.Examples) > 0 {
		md.WriteString("## 使用示例\n\n")
		for _, example := range description.Examples {
			md.WriteString(fmt.Sprintf("### %s\n\n", example.Capability))
			md.WriteString(fmt.Sprintf("**用户输入**: %s  \n", example.UserInput))
			md.WriteString(fmt.Sprintf("**预期时间**: %s  \n", example.ExpectedTime))
			if len(example.Parameters) > 0 {
				paramJSON, _ := json.MarshalIndent(example.Parameters, "", "  ")
				md.WriteString(fmt.Sprintf("**参数**:\n```json\n%s\n```\n\n", string(paramJSON)))
			}
		}
	}

	if len(description.Constraints) > 0 {
		md.WriteString("## 约束条件\n\n")
		for _, constraint := range description.Constraints {
			md.WriteString(fmt.Sprintf("- **%s** (%s): %s\n", constraint.Type, constraint.Severity, constraint.Description))
		}
		md.WriteString("\n")
	}

	return md.String()
}

// GenerateJSONDescription 生成JSON格式的能力描述
func (acd *AgentCapabilityDescriptor) GenerateJSONDescription(description *CapabilityDescription) (string, error) {
	jsonData, err := json.MarshalIndent(description, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal capability description: %w", err)
	}
	return string(jsonData), nil
}
