package model

import (
	"aiops-platform/internal/security"
	"time"
)

// CommandAuditLog 命令审计日志模型
type CommandAuditLog struct {
	ID         int64                     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     int64                     `gorm:"not null;index" json:"user_id"`
	HostID     int64                     `gorm:"not null;index" json:"host_id"`
	Command    string                    `gorm:"type:text;not null" json:"command"`
	RiskLevel  security.CommandRiskLevel `gorm:"type:varchar(20);not null;index" json:"risk_level"`
	IsAllowed  bool                      `gorm:"not null;index" json:"is_allowed"`
	ExecutedAt *time.Time                `gorm:"index" json:"executed_at"`
	Result     string                    `gorm:"type:text" json:"result"`
	ExitCode   int                       `gorm:"default:0" json:"exit_code"`
	Duration   int                       `gorm:"default:0" json:"duration"` // 执行时间（毫秒）
	IPAddress  string                    `gorm:"type:varchar(45);index" json:"ip_address"`
	UserAgent  string                    `gorm:"type:text" json:"user_agent"`
	SessionID  string                    `gorm:"type:varchar(100);index" json:"session_id"`
	CreatedAt  time.Time                 `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time                 `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (CommandAuditLog) TableName() string {
	return "command_audit_logs"
}

// SecurityEvent 安全事件模型
type SecurityEvent struct {
	ID          int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID      int64      `gorm:"not null;index" json:"user_id"`
	EventType   string     `gorm:"type:varchar(50);not null;index" json:"event_type"` // login, command_blocked, permission_denied, etc.
	Severity    string     `gorm:"type:varchar(20);not null;index" json:"severity"`   // low, medium, high, critical
	Description string     `gorm:"type:text;not null" json:"description"`
	Details     string     `gorm:"type:json" json:"details"` // JSON格式的详细信息
	IPAddress   string     `gorm:"type:varchar(45);index" json:"ip_address"`
	UserAgent   string     `gorm:"type:text" json:"user_agent"`
	SessionID   string     `gorm:"type:varchar(100);index" json:"session_id"`
	Resolved    bool       `gorm:"default:false;index" json:"resolved"`
	ResolvedBy  *int64     `gorm:"index" json:"resolved_by"`
	ResolvedAt  *time.Time `json:"resolved_at"`
	CreatedAt   time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (SecurityEvent) TableName() string {
	return "security_events"
}

// UserSession 用户会话模型
type UserSession struct {
	ID           int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID       int64     `gorm:"not null;index" json:"user_id"`
	SessionID    string    `gorm:"type:varchar(100);uniqueIndex" json:"session_id"`
	IPAddress    string    `gorm:"type:varchar(45);index" json:"ip_address"`
	UserAgent    string    `gorm:"type:text" json:"user_agent"`
	IsActive     bool      `gorm:"default:true;index" json:"is_active"`
	LastActivity time.Time `gorm:"index" json:"last_activity"`
	ExpiresAt    time.Time `gorm:"index" json:"expires_at"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (UserSession) TableName() string {
	return "user_sessions"
}

// 注意：Permission、Role、UserRole、RolePermission 模型已在 rbac.go 中定义

// HostPermission 主机权限模型
type HostPermission struct {
	ID         int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     int64      `gorm:"not null;index" json:"user_id"`
	HostID     int64      `gorm:"not null;index" json:"host_id"`
	Permission string     `gorm:"type:varchar(50);not null" json:"permission"` // read, execute, admin
	GrantedBy  int64      `gorm:"not null" json:"granted_by"`
	GrantedAt  time.Time  `gorm:"autoCreateTime" json:"granted_at"`
	ExpiresAt  *time.Time `json:"expires_at"`
	CreatedAt  time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (HostPermission) TableName() string {
	return "host_permissions"
}

// 注意：SystemConfig 模型已在 common.go 中定义

// AuditLogQuery 审计日志查询参数
type AuditLogQuery struct {
	UserID    *int64                     `json:"user_id"`
	HostID    *int64                     `json:"host_id"`
	RiskLevel *security.CommandRiskLevel `json:"risk_level"`
	IsAllowed *bool                      `json:"is_allowed"`
	StartTime *time.Time                 `json:"start_time"`
	EndTime   *time.Time                 `json:"end_time"`
	IPAddress string                     `json:"ip_address"`
	SessionID string                     `json:"session_id"`
	Command   string                     `json:"command"` // 模糊搜索
	Page      int                        `json:"page"`
	Limit     int                        `json:"limit"`
	OrderBy   string                     `json:"order_by"`
	OrderDir  string                     `json:"order_dir"`
}

// SecurityEventQuery 安全事件查询参数
type SecurityEventQuery struct {
	UserID    *int64     `json:"user_id"`
	EventType string     `json:"event_type"`
	Severity  string     `json:"severity"`
	Resolved  *bool      `json:"resolved"`
	StartTime *time.Time `json:"start_time"`
	EndTime   *time.Time `json:"end_time"`
	IPAddress string     `json:"ip_address"`
	SessionID string     `json:"session_id"`
	Page      int        `json:"page"`
	Limit     int        `json:"limit"`
	OrderBy   string     `json:"order_by"`
	OrderDir  string     `json:"order_dir"`
}

// AuditLogResponse 审计日志响应
type AuditLogResponse struct {
	Total int64              `json:"total"`
	Page  int                `json:"page"`
	Limit int                `json:"limit"`
	Logs  []*CommandAuditLog `json:"logs"`
}

// SecurityEventResponse 安全事件响应
type SecurityEventResponse struct {
	Total  int64            `json:"total"`
	Page   int              `json:"page"`
	Limit  int              `json:"limit"`
	Events []*SecurityEvent `json:"events"`
}
