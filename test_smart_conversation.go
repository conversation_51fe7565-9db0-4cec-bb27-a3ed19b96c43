package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// 测试智能对话流程
func main() {
	baseURL := "http://localhost:8080"

	fmt.Println("🚀 测试AI运维管理平台智能对话流程")
	fmt.Println("====================================================")

	// 测试场景1：完整参数格式
	fmt.Println("\n📋 测试场景1：完整参数格式")
	testCompleteParams(baseURL)

	// 等待一下
	time.Sleep(2 * time.Second)

	// 测试场景2：分步参数收集
	fmt.Println("\n📋 测试场景2：分步参数收集")
	testStepByStepParams(baseURL)

	// 等待一下
	time.Sleep(2 * time.Second)

	// 测试场景3：确认流程
	fmt.Println("\n📋 测试场景3：确认流程")
	testConfirmationFlow(baseURL)
}

// 测试完整参数格式
func testCompleteParams(baseURL string) {
	// 先创建会话
	sessionID := createChatSession(baseURL, "测试完整参数格式")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	// 发送完整的主机添加请求
	message := "添加主机 ************** root 1qaz#EDC"
	response := sendChatMessage(baseURL, sessionID, message)

	fmt.Printf("用户输入: %s\n", message)
	fmt.Printf("AI回复: %s\n", response.AIResponse)
	fmt.Printf("意图: %s\n", response.Intent)

	// 如果AI询问确认，回复"是"
	if strings.Contains(response.AIResponse, "是否要添加主机") {
		confirmResponse := sendChatMessage(baseURL, sessionID, "是")
		fmt.Printf("用户确认: 是\n")
		fmt.Printf("AI执行结果: %s\n", confirmResponse.Content)
	}
}

// 测试分步参数收集
func testStepByStepParams(baseURL string) {
	// 先创建会话
	sessionID := createChatSession(baseURL, "测试分步参数收集")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	// 第一步：只说要添加主机
	response1 := sendChatMessage(baseURL, sessionID, "主机添加")
	fmt.Printf("用户输入: 主机添加\n")
	fmt.Printf("AI回复: %s\n", response1.Content)

	// 第二步：提供参数
	response2 := sendChatMessage(baseURL, sessionID, "10.0.0.100 admin P@ssw0rd")
	fmt.Printf("用户输入: 10.0.0.100 admin P@ssw0rd\n")
	fmt.Printf("AI回复: %s\n", response2.Content)

	// 第三步：确认
	if strings.Contains(response2.AIResponse, "是否要添加主机") {
		response3 := sendChatMessage(baseURL, sessionID, "是")
		fmt.Printf("用户确认: 是\n")
		fmt.Printf("AI执行结果: %s\n", response3.Content)
	}
}

// 测试确认流程
func testConfirmationFlow(baseURL string) {
	// 先创建会话
	sessionID := createChatSession(baseURL, "测试确认流程")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}

	// 发送主机添加请求
	response1 := sendChatMessage(baseURL, sessionID, "172.16.1.50 ubuntu mypassword123")
	fmt.Printf("用户输入: 172.16.1.50 ubuntu mypassword123\n")
	fmt.Printf("AI回复: %s\n", response1.Content)

	// 先拒绝
	if strings.Contains(response1.AIResponse, "是否要添加主机") {
		response2 := sendChatMessage(baseURL, sessionID, "否")
		fmt.Printf("用户回复: 否\n")
		fmt.Printf("AI回复: %s\n", response2.Content)

		// 重新发起请求
		response3 := sendChatMessage(baseURL, sessionID, "172.16.1.50 ubuntu mypassword123")
		fmt.Printf("用户重新输入: 172.16.1.50 ubuntu mypassword123\n")
		fmt.Printf("AI回复: %s\n", response3.Content)

		// 这次确认
		if strings.Contains(response3.AIResponse, "是否要添加主机") {
			response4 := sendChatMessage(baseURL, sessionID, "是")
			fmt.Printf("用户确认: 是\n")
			fmt.Printf("AI执行结果: %s\n", response4.Content)
		}
	}
}

// ChatRequest 聊天请求
type ChatRequest struct {
	SessionID string `json:"session_id"`
	Content   string `json:"content"`
	Stream    bool   `json:"stream"`
}

// ChatResponse 聊天响应
type ChatResponse struct {
	ID               int64                    `json:"id"`
	SessionID        int64                    `json:"session_id"`
	MessageType      string                   `json:"message_type"`
	Content          string                   `json:"content"`
	AIResponse       string                   `json:"ai_response"`
	Intent           string                   `json:"intent"`
	ExtractedParams  map[string]interface{}   `json:"extracted_params"`
	ToolCalls        []map[string]interface{} `json:"tool_calls"`
	ToolResults      []map[string]interface{} `json:"tool_results"`
	TokenCount       int                      `json:"token_count"`
	ProcessingTimeMs int                      `json:"processing_time_ms"`
	ErrorMessage     string                   `json:"error_message"`
}

// 发送聊天消息
func sendChatMessage(baseURL, sessionID, message string) *ChatResponse {
	url := baseURL + "/api/v1/chat/message"

	reqBody := ChatRequest{
		SessionID: sessionID,
		Content:   message,
		Stream:    false,
	}

	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return &ChatResponse{Content: "请求失败"}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return &ChatResponse{Content: "读取响应失败"}
	}

	var apiResponse struct {
		Code    int          `json:"code"`
		Message string       `json:"message"`
		Data    ChatResponse `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResponse); err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return &ChatResponse{Content: "解析响应失败"}
	}

	if apiResponse.Code != 200 {
		fmt.Printf("API错误: %s\n", apiResponse.Message)
		return &ChatResponse{Content: apiResponse.Message}
	}

	return &apiResponse.Data
}

// createChatSession 创建聊天会话
func createChatSession(baseURL, title string) string {
	url := baseURL + "/api/v1/chat/sessions"

	reqBody := map[string]interface{}{
		"title": title,
	}

	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建会话请求失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取会话响应失败: %v\n", err)
		return ""
	}

	var apiResponse struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			SessionID string `json:"session_id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResponse); err != nil {
		fmt.Printf("解析会话响应失败: %v\n", err)
		return ""
	}

	if apiResponse.Code != 201 {
		fmt.Printf("创建会话失败: %s\n", apiResponse.Message)
		return ""
	}

	return apiResponse.Data.SessionID
}
