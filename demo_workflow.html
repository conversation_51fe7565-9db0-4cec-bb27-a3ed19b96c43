<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能工作流系统演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .workflow-step {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        
        .workflow-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .step-number {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            height: 100%;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .api-example {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #ff6b6b;
        }
        
        .architecture-diagram {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
        }
        
        .layer {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .workflow-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 15px 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            display: none;
        }
        
        .workflow-indicator.active {
            display: block;
            animation: slideInRight 0.5s ease-out;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .chat-simulation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
        }
        
        .ai-message {
            background: #e9ecef;
            color: #333;
        }
        
        .system-message {
            background: #28a745;
            color: white;
            text-align: center;
            margin: 15px auto;
            max-width: 90%;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 标题 -->
        <div class="text-center mb-5">
            <h1 class="text-white display-4 fw-bold mb-3">
                <i class="bi bi-robot"></i> 智能对话式工作流系统
            </h1>
            <p class="text-white-50 fs-5">AI驱动的运维管理平台 - 让复杂操作变得简单</p>
        </div>

        <!-- 工作流指示器 -->
        <div class="workflow-indicator" id="workflowIndicator">
            <i class="bi bi-gear-fill me-2"></i>
            <span>主机管理工作流进行中...</span>
        </div>

        <!-- 核心特性 -->
        <div class="demo-card p-5 mb-5">
            <h2 class="text-center mb-4"><i class="bi bi-stars"></i> 核心特性</h2>
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="feature-card text-center">
                        <i class="bi bi-brain display-4 mb-3"></i>
                        <h5>智能意图识别</h5>
                        <p>DeepSeek API驱动的自然语言理解</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="feature-card text-center">
                        <i class="bi bi-diagram-3 display-4 mb-3"></i>
                        <h5>动态流程引导</h5>
                        <p>智能分析状态，提供下一步建议</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="feature-card text-center">
                        <i class="bi bi-database display-4 mb-3"></i>
                        <h5>数据持久化</h5>
                        <p>实时保存状态，确保一致性</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="feature-card text-center">
                        <i class="bi bi-memory display-4 mb-3"></i>
                        <h5>上下文记忆</h5>
                        <p>记住操作历史，避免重复询问</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统架构 -->
        <div class="demo-card p-5 mb-5">
            <h2 class="text-center mb-4"><i class="bi bi-diagram-2"></i> 系统架构</h2>
            <div class="architecture-diagram">
                <div class="layer">
                    <h5><i class="bi bi-brain"></i> AI智能层</h5>
                    <p>DeepSeek API集成 • 智能状态分析器 • 动态建议生成器</p>
                </div>
                <div class="layer">
                    <h5><i class="bi bi-gear"></i> 工作流引擎层</h5>
                    <p>工作流定义管理器 • 状态机引擎 • 步骤执行器 • 流程调度器</p>
                </div>
                <div class="layer">
                    <h5><i class="bi bi-database"></i> 上下文管理层</h5>
                    <p>会话上下文管理器 • 工作流状态存储 • 参数收集器 • 历史记录管理器</p>
                </div>
                <div class="layer">
                    <h5><i class="bi bi-tools"></i> 业务执行层</h5>
                    <p>主机管理执行器 • 监控告警执行器 • 统计报表执行器 • 系统操作执行器</p>
                </div>
            </div>
        </div>

        <!-- 工作流演示 -->
        <div class="demo-card p-5 mb-5">
            <h2 class="text-center mb-4"><i class="bi bi-play-circle"></i> 主机管理工作流演示</h2>
            
            <div class="highlight mb-4">
                <h5><i class="bi bi-lightbulb"></i> 场景：用户想要添加一台新主机</h5>
                <p class="mb-0">系统将智能引导用户完成整个添加流程，无需记忆复杂的操作步骤</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">工作流步骤</h5>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">1</div>
                            <div>
                                <h6 class="mb-1">意图识别</h6>
                                <small>AI分析用户输入："我想添加一台新主机"</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">2</div>
                            <div>
                                <h6 class="mb-1">状态检查</h6>
                                <small>检查数据库中是否有现有主机记录</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">3</div>
                            <div>
                                <h6 class="mb-1">智能引导</h6>
                                <small>AI分析情况并提供个性化引导消息</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">4</div>
                            <div>
                                <h6 class="mb-1">参数收集</h6>
                                <small>引导用户提供主机信息（IP、端口、用户名等）</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">5</div>
                            <div>
                                <h6 class="mb-1">数据验证</h6>
                                <small>验证用户输入的有效性和完整性</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">6</div>
                            <div>
                                <h6 class="mb-1">保存数据</h6>
                                <small>将主机信息保存到数据库</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">7</div>
                            <div>
                                <h6 class="mb-1">连接测试</h6>
                                <small>测试到主机的SSH连接</small>
                            </div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="d-flex align-items-center">
                            <div class="step-number">8</div>
                            <div>
                                <h6 class="mb-1">完成确认</h6>
                                <small>发送完成消息并提供后续操作建议</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="mb-3">对话模拟</h5>
                    <div class="chat-simulation">
                        <div class="message user-message">
                            我想添加一台新主机
                        </div>
                        <div class="message ai-message">
                            <i class="bi bi-robot me-2"></i>
                            当前没有主机记录，我来帮您添加主机。请提供主机信息：<br>
                            • IP地址<br>
                            • 主机名<br>
                            • SSH端口<br>
                            • 用户名
                        </div>
                        <div class="message user-message">
                            主机名：web-server-01<br>
                            IP：*************<br>
                            端口：22<br>
                            用户名：admin
                        </div>
                        <div class="message system-message">
                            <i class="bi bi-check-circle me-2"></i>
                            正在验证主机信息...
                        </div>
                        <div class="message system-message">
                            <i class="bi bi-database me-2"></i>
                            正在保存主机信息...
                        </div>
                        <div class="message system-message">
                            <i class="bi bi-wifi me-2"></i>
                            正在测试SSH连接...
                        </div>
                        <div class="message ai-message">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            🎉 主机 'web-server-01' 已成功添加到系统中！<br><br>
                            ✅ 主机信息已保存<br>
                            ✅ SSH连接测试通过<br>
                            ✅ 主机已准备就绪<br><br>
                            您现在可以使用以下命令管理这台主机：<br>
                            • 查看主机状态<br>
                            • 执行系统命令<br>
                            • 监控系统指标
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <button class="demo-button" onclick="startWorkflowDemo()">
                    <i class="bi bi-play-fill me-2"></i>开始演示
                </button>
            </div>
        </div>

        <!-- API示例 -->
        <div class="demo-card p-5 mb-5">
            <h2 class="text-center mb-4"><i class="bi bi-code-slash"></i> API接口示例</h2>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <h5>分析用户意图</h5>
                    <div class="api-example">
<pre><code>// 分析用户意图
const response = await fetch('/api/v1/workflow/analyze-intent', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        message: "我想添加一台新主机",
        session_id: "session_123"
    })
});

const result = await response.json();
console.log(result.needs_workflow); // true
console.log(result.workflow_type);  // "host_management"</code></pre>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <h5>触发工作流</h5>
                    <div class="api-example">
<pre><code>// 触发工作流
const response = await fetch('/api/v1/workflow/trigger', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        trigger_type: 'intent',
        intent: 'host_management',
        session_id: 'session_123',
        user_id: 1,
        priority: 1
    })
});

const result = await response.json();
console.log(result.instance_id); // "wf_1234567890"</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支持的工作流类型 -->
        <div class="demo-card p-5 mb-5">
            <h2 class="text-center mb-4"><i class="bi bi-list-check"></i> 支持的工作流类型</h2>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <i class="bi bi-server text-primary fs-3 me-3"></i>
                        <div>
                            <h6 class="mb-1">主机管理</h6>
                            <small class="text-muted">添加、查看、删除主机，测试连接</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <i class="bi bi-terminal text-success fs-3 me-3"></i>
                        <div>
                            <h6 class="mb-1">命令执行</h6>
                            <small class="text-muted">远程命令执行工作流</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <i class="bi bi-graph-up text-info fs-3 me-3"></i>
                        <div>
                            <h6 class="mb-1">系统监控</h6>
                            <small class="text-muted">系统监控工作流</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <i class="bi bi-exclamation-triangle text-warning fs-3 me-3"></i>
                        <div>
                            <h6 class="mb-1">告警管理</h6>
                            <small class="text-muted">告警管理工作流</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <i class="bi bi-file-earmark-text text-secondary fs-3 me-3"></i>
                        <div>
                            <h6 class="mb-1">报表生成</h6>
                            <small class="text-muted">报表生成工作流</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <i class="bi bi-shield-check text-danger fs-3 me-3"></i>
                        <div>
                            <h6 class="mb-1">安全审计</h6>
                            <small class="text-muted">安全审计工作流</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="demo-card p-5 text-center">
            <h2 class="mb-4"><i class="bi bi-check-circle-fill text-success"></i> 系统优势</h2>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <i class="bi bi-brain display-6 text-primary mb-3"></i>
                    <h5>智能化</h5>
                    <p class="text-muted">AI驱动的意图识别和流程引导</p>
                </div>
                <div class="col-md-3 mb-3">
                    <i class="bi bi-gear-wide-connected display-6 text-success mb-3"></i>
                    <h5>自动化</h5>
                    <p class="text-muted">完整的工作流自动执行</p>
                </div>
                <div class="col-md-3 mb-3">
                    <i class="bi bi-shield-check display-6 text-info mb-3"></i>
                    <h5>可靠性</h5>
                    <p class="text-muted">状态持久化和错误恢复</p>
                </div>
                <div class="col-md-3 mb-3">
                    <i class="bi bi-puzzle display-6 text-warning mb-3"></i>
                    <h5>可扩展</h5>
                    <p class="text-muted">模块化设计，易于扩展</p>
                </div>
            </div>
            <div class="mt-4">
                <h4 class="text-primary">🚀 系统已经准备就绪，可以开始处理复杂的运维操作流程！</h4>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startWorkflowDemo() {
            const indicator = document.getElementById('workflowIndicator');
            indicator.classList.add('active');
            
            // 模拟工作流进度
            setTimeout(() => {
                indicator.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i><span>主机管理工作流已完成！</span>';
                indicator.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            }, 3000);
            
            setTimeout(() => {
                indicator.classList.remove('active');
            }, 6000);
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
