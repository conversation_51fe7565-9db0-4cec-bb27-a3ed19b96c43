package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

func main() {
	baseURL := "http://localhost:8080"

	fmt.Println("🚀 完整智能对话流程测试")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 创建会话
	sessionID := createSession(baseURL, "完整流程测试")
	if sessionID == "" {
		fmt.Println("❌ 创建会话失败")
		return
	}
	fmt.Printf("✅ 会话创建成功: %s\n", sessionID)

	// 步骤1：发送主机添加请求
	fmt.Println("\n📋 步骤1：发送主机添加请求")
	message1 := "添加主机 ************** root 1qaz#EDC"
	fmt.Printf("📤 用户: %s\n", message1)

	response1 := sendMessage(baseURL, sessionID, message1)
	fmt.Printf("🤖 AI: %s\n", response1.Data.AIResponse)

	// 检查是否是确认询问
	if strings.Contains(response1.Data.AIResponse, "是否要添加主机") {
		fmt.Println("✅ AI正确询问确认")

		// 步骤2：用户确认
		fmt.Println("\n📋 步骤2：用户确认操作")
		message2 := "是"
		fmt.Printf("📤 用户: %s\n", message2)

		time.Sleep(1 * time.Second)
		response2 := sendMessage(baseURL, sessionID, message2)
		fmt.Printf("🤖 AI: %s\n", response2.Data.AIResponse)

		if strings.Contains(response2.Data.AIResponse, "添加成功") || strings.Contains(response2.Data.AIResponse, "✅") {
			fmt.Println("✅ 主机添加操作执行成功")
		} else {
			fmt.Println("⚠️ 主机添加操作可能未成功执行")
		}
	} else {
		fmt.Println("❌ AI没有询问确认，而是返回了其他内容")
	}

	fmt.Println("\n🎯 测试完成！")
}

type SessionResponse struct {
	Code int `json:"code"`
	Data struct {
		SessionID string `json:"session_id"`
	} `json:"data"`
}

type MessageResponse struct {
	Code int `json:"code"`
	Data struct {
		AIResponse string `json:"ai_response"`
		Intent     string `json:"intent"`
	} `json:"data"`
}

func createSession(baseURL, title string) string {
	url := baseURL + "/api/v1/chat/sessions"

	reqBody := map[string]string{"title": title}
	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建会话失败: %v\n", err)
		return ""
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var sessionResp SessionResponse
	if err := json.Unmarshal(body, &sessionResp); err != nil {
		fmt.Printf("解析会话响应失败: %v\n", err)
		return ""
	}

	return sessionResp.Data.SessionID
}

func sendMessage(baseURL, sessionID, content string) *MessageResponse {
	url := baseURL + "/api/v1/chat/message"

	reqBody := map[string]interface{}{
		"session_id": sessionID,
		"content":    content,
		"stream":     false,
	}

	jsonData, _ := json.Marshal(reqBody)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("发送消息失败: %v\n", err)
		return &MessageResponse{}
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var msgResp MessageResponse
	if err := json.Unmarshal(body, &msgResp); err != nil {
		fmt.Printf("解析消息响应失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return &MessageResponse{}
	}

	return &msgResp
}
