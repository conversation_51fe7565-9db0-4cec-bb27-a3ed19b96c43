package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	baseURL := "http://localhost:8766"

	fmt.Println("=== 简化测试主机查询功能 ===")

	// 测试1：直接测试list_hosts API
	fmt.Println("\n1. 测试list_hosts API")
	testListHostsAPI(baseURL)

	// 测试2：测试聊天接口（不需要认证的）
	fmt.Println("\n2. 测试聊天接口")
	testChatAPI(baseURL)
}

func testListHostsAPI(baseURL string) {
	response := makeRequest("GET", baseURL+"/api/v1/hosts", nil)
	if response != nil {
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err == nil {
			if data, ok := result["data"].(map[string]interface{}); ok {
				if hosts, ok := data["hosts"].([]interface{}); ok {
					fmt.Printf("✅ 主机数量: %d\n", len(hosts))
					if len(hosts) > 0 {
						host := hosts[0].(map[string]interface{})
						fmt.Printf("示例主机: ID=%v, 名称=%v, IP=%v, 用户名=%v, 环境=%v\n",
							host["id"], host["name"], host["ip_address"], host["username"], host["environment"])

						// 检查是否包含密码（应该不包含）
						if _, hasPassword := host["password"]; hasPassword {
							fmt.Println("❌ 错误：返回了密码信息")
						} else {
							fmt.Println("✅ 正确：未返回密码信息")
						}
					}
				}
			}
		} else {
			fmt.Printf("响应: %s\n", string(response))
		}
	}
}

func testChatAPI(baseURL string) {
	data := map[string]interface{}{
		"message": "查询现有主机账号",
	}

	response := makeRequest("POST", baseURL+"/api/chat", data)
	if response != nil {
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err == nil {
			if content, ok := result["content"].(string); ok {
				fmt.Printf("聊天响应: %s\n", content[:min(300, len(content))])

				// 检查是否包含主机信息
				if containsHostInfo(content) {
					fmt.Println("✅ 包含主机相关信息")
				} else {
					fmt.Println("❌ 未包含主机相关信息")
				}
			}
		} else {
			fmt.Printf("聊天响应解析失败: %s\n", string(response))
		}
	}
}

func containsHostInfo(content string) bool {
	keywords := []string{"主机", "IP", "用户名", "端口", "192.168", "root", "账号"}
	count := 0
	for _, keyword := range keywords {
		if contains(content, keyword) {
			count++
		}
	}
	return count >= 2 // 至少包含2个相关关键词
}

func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func makeRequest(method, url string, data interface{}) []byte {
	var body io.Reader

	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			fmt.Printf("JSON编码失败: %v\n", err)
			return nil
		}
		body = bytes.NewBuffer(jsonData)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return nil
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return nil
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return nil
	}

	fmt.Printf("状态码: %d\n", resp.StatusCode)
	return responseBody
}
