package service

import (
	"fmt"

	"aiops-platform/internal/model"
	"aiops-platform/internal/security"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// PermissionService 权限服务接口
type PermissionService interface {
	CheckPermission(userID int64, resource string, action string) (bool, error)
	GetUserRoles(userID int64) ([]string, error)
	HasRole(userID int64, role string) (bool, error)
	CanExecuteCommand(userID int64, hostID int64, command string) (bool, error)
}

// permissionService 权限服务实现
type permissionService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewPermissionService 创建权限服务
func NewPermissionService(db *gorm.DB, logger *logrus.Logger) PermissionService {
	return &permissionService{
		db:     db,
		logger: logger,
	}
}

// CheckPermission 检查用户权限
func (ps *permissionService) CheckPermission(userID int64, resource string, action string) (bool, error) {
	permissionName := fmt.Sprintf("%s.%s", resource, action)

	var count int64
	err := ps.db.Table("user_roles ur").
		Joins("JOIN role_permissions rp ON ur.role_id = rp.role_id").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("ur.user_id = ? AND p.name = ? AND (ur.expires_at IS NULL OR ur.expires_at > NOW())", userID, permissionName).
		Count(&count).Error

	if err != nil {
		ps.logger.WithError(err).Error("Failed to check permission")
		return false, err
	}

	return count > 0, nil
}

// GetUserRoles 获取用户角色
func (ps *permissionService) GetUserRoles(userID int64) ([]string, error) {
	var roles []string

	err := ps.db.Table("user_roles ur").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ? AND (ur.expires_at IS NULL OR ur.expires_at > NOW())", userID).
		Pluck("r.name", &roles).Error

	if err != nil {
		ps.logger.WithError(err).Error("Failed to get user roles")
		return nil, err
	}

	return roles, nil
}

// HasRole 检查用户是否有指定角色
func (ps *permissionService) HasRole(userID int64, role string) (bool, error) {
	roles, err := ps.GetUserRoles(userID)
	if err != nil {
		return false, err
	}

	for _, r := range roles {
		if r == role {
			return true, nil
		}
	}

	return false, nil
}

// CanExecuteCommand 检查用户是否可以执行命令
func (ps *permissionService) CanExecuteCommand(userID int64, hostID int64, command string) (bool, error) {
	// 1. 检查基本的主机执行权限
	hasHostExecute, err := ps.CheckPermission(userID, "host", "execute")
	if err != nil {
		return false, err
	}

	if !hasHostExecute {
		return false, nil
	}

	// 2. 检查主机特定权限
	hasHostPermission, err := ps.checkHostPermission(userID, hostID, "execute")
	if err != nil {
		return false, err
	}

	if !hasHostPermission {
		return false, nil
	}

	// 3. 根据命令风险等级检查权限
	checker := security.NewCommandSecurityChecker(ps.logger)
	securityResult := checker.CheckCommand(command)

	permissionName := ps.getCommandPermissionName(securityResult.RiskLevel)
	hasCommandPermission, err := ps.CheckPermission(userID, "command", permissionName)
	if err != nil {
		return false, err
	}

	return hasCommandPermission, nil
}

// checkHostPermission 检查主机特定权限
func (ps *permissionService) checkHostPermission(userID int64, hostID int64, permission string) (bool, error) {
	var count int64
	err := ps.db.Model(&model.HostPermission{}).
		Where("user_id = ? AND host_id = ? AND permission = ? AND (expires_at IS NULL OR expires_at > NOW())",
			userID, hostID, permission).
		Count(&count).Error

	if err != nil {
		ps.logger.WithError(err).Error("Failed to check host permission")
		return false, err
	}

	// 如果没有特定的主机权限，检查是否是管理员
	if count == 0 {
		isAdmin, err := ps.HasRole(userID, "admin")
		if err != nil {
			return false, err
		}
		return isAdmin, nil
	}

	return count > 0, nil
}

// getCommandPermissionName 根据风险等级获取权限名称
func (ps *permissionService) getCommandPermissionName(riskLevel security.CommandRiskLevel) string {
	switch riskLevel {
	case security.RiskLevelSafe:
		return "execute_safe"
	case security.RiskLevelLow:
		return "execute_low_risk"
	case security.RiskLevelMedium:
		return "execute_medium_risk"
	case security.RiskLevelHigh:
		return "execute_high_risk"
	case security.RiskLevelCritical:
		return "execute_critical_risk"
	default:
		return "execute_safe"
	}
}

// GrantUserRole 授予用户角色
func (ps *permissionService) GrantUserRole(userID int64, roleName string, grantedBy int64) error {
	// 查找角色ID
	var role model.Role
	err := ps.db.Where("name = ?", roleName).First(&role).Error
	if err != nil {
		return fmt.Errorf("角色不存在: %s", roleName)
	}

	// 检查是否已经有该角色
	var count int64
	err = ps.db.Model(&model.UserRole{}).
		Where("user_id = ? AND role_id = ?", userID, role.ID).
		Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("用户已经拥有角色: %s", roleName)
	}

	// 创建用户角色关联
	userRole := &model.UserRole{
		UserID:    userID,
		RoleID:    role.ID,
		GrantedBy: grantedBy,
	}

	return ps.db.Create(userRole).Error
}

// RevokeUserRole 撤销用户角色
func (ps *permissionService) RevokeUserRole(userID int64, roleName string) error {
	return ps.db.Table("user_roles ur").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ? AND r.name = ?", userID, roleName).
		Delete(&model.UserRole{}).Error
}

// GrantHostPermission 授予主机权限
func (ps *permissionService) GrantHostPermission(userID int64, hostID int64, permission string, grantedBy int64) error {
	// 检查是否已经有该权限
	var count int64
	err := ps.db.Model(&model.HostPermission{}).
		Where("user_id = ? AND host_id = ? AND permission = ?", userID, hostID, permission).
		Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("用户已经拥有该主机权限")
	}

	// 创建主机权限
	hostPermission := &model.HostPermission{
		UserID:     userID,
		HostID:     hostID,
		Permission: permission,
		GrantedBy:  grantedBy,
	}

	return ps.db.Create(hostPermission).Error
}

// RevokeHostPermission 撤销主机权限
func (ps *permissionService) RevokeHostPermission(userID int64, hostID int64, permission string) error {
	return ps.db.Where("user_id = ? AND host_id = ? AND permission = ?", userID, hostID, permission).
		Delete(&model.HostPermission{}).Error
}

// GetUserPermissions 获取用户所有权限
func (ps *permissionService) GetUserPermissions(userID int64) ([]string, error) {
	var permissions []string

	err := ps.db.Table("user_roles ur").
		Joins("JOIN role_permissions rp ON ur.role_id = rp.role_id").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("ur.user_id = ? AND (ur.expires_at IS NULL OR ur.expires_at > NOW())", userID).
		Pluck("p.name", &permissions).Error

	if err != nil {
		ps.logger.WithError(err).Error("Failed to get user permissions")
		return nil, err
	}

	return permissions, nil
}

// InitializeDefaultUserRole 为新用户初始化默认角色
func (ps *permissionService) InitializeDefaultUserRole(userID int64) error {
	// 获取默认角色配置
	var config model.SystemConfig
	err := ps.db.Where("config_key = ?", "system.default_user_role").First(&config).Error
	if err != nil {
		// 如果没有配置，使用默认的 "user" 角色
		config.ConfigValue = "user"
	}

	return ps.GrantUserRole(userID, config.ConfigValue, 1) // 系统自动授权
}

// MockPermissionService 模拟权限服务（用于开发测试）
type MockPermissionService struct {
	logger *logrus.Logger
}

// NewMockPermissionService 创建模拟权限服务
func NewMockPermissionService(logger *logrus.Logger) PermissionService {
	return &MockPermissionService{
		logger: logger,
	}
}

// CheckPermission 模拟权限检查（开发阶段返回true）
func (mps *MockPermissionService) CheckPermission(userID int64, resource string, action string) (bool, error) {
	mps.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"resource": resource,
		"action":   action,
	}).Debug("Mock permission check - allowing all")
	return true, nil
}

// GetUserRoles 模拟获取用户角色
func (mps *MockPermissionService) GetUserRoles(userID int64) ([]string, error) {
	return []string{"admin"}, nil
}

// HasRole 模拟角色检查
func (mps *MockPermissionService) HasRole(userID int64, role string) (bool, error) {
	return true, nil
}

// CanExecuteCommand 模拟命令执行权限检查
func (mps *MockPermissionService) CanExecuteCommand(userID int64, hostID int64, command string) (bool, error) {
	// 在开发阶段，只阻止极高风险命令
	checker := security.NewCommandSecurityChecker(mps.logger)
	securityResult := checker.CheckCommand(command)

	// 阻止极高风险命令
	if securityResult.RiskLevel == security.RiskLevelCritical {
		return false, nil
	}

	return true, nil
}
