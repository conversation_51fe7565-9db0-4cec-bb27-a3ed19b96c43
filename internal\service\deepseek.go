package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"aiops-platform/internal/config"

	"github.com/sirupsen/logrus"
)

// DeepSeekService DeepSeek AI服务
type DeepSeekService struct {
	config     *config.DeepSeekConfig
	httpClient *http.Client
	logger     *logrus.Logger
}

// NewDeepSeekService 创建DeepSeek服务
func NewDeepSeekService(cfg *config.DeepSeekConfig, logger *logrus.Logger) *DeepSeekService {
	return &DeepSeekService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: cfg.Timeout,
		},
		logger: logger,
	}
}

// ChatRequest 对话请求
type ChatRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Tools       []Tool    `json:"tools,omitempty"`
	ToolChoice  string    `json:"tool_choice,omitempty"`
	Temperature float64   `json:"temperature,omitempty"`
	TopP        float64   `json:"top_p,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Stream      bool      `json:"stream,omitempty"`
}

// Message 消息
type Message struct {
	Role       string     `json:"role"`
	Content    string     `json:"content,omitempty"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
}

// ChatResponse 对话响应
type ChatResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice 选择
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// Usage 使用情况
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ToolFunction 工具函数定义
type ToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// Tool 工具定义
type Tool struct {
	Type     string       `json:"type"`
	Function ToolFunction `json:"function"`
}

// ToolCall 工具调用
type ToolCall struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Function struct {
		Name      string `json:"name"`
		Arguments string `json:"arguments"`
	} `json:"function"`
}

// Chat 发送对话请求
func (s *DeepSeekService) Chat(ctx context.Context, messages []Message) (*ChatResponse, error) {
	request := &ChatRequest{
		Model:       s.config.Model,
		Messages:    messages,
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
		MaxTokens:   s.config.MaxContextTokens,
		Stream:      false,
	}

	return s.sendChatRequest(ctx, request)
}

// ChatWithTools 发送带工具的对话请求
func (s *DeepSeekService) ChatWithTools(ctx context.Context, messages []Message, tools []Tool) (*ChatResponse, error) {
	request := &ChatRequest{
		Model:       s.config.Model,
		Messages:    messages,
		Tools:       tools,
		ToolChoice:  "auto",
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
		MaxTokens:   s.config.MaxContextTokens,
		Stream:      false,
	}

	return s.sendChatRequest(ctx, request)
}

// ChatStream 发送流式对话请求
func (s *DeepSeekService) ChatStream(ctx context.Context, messages []Message, callback func(string) error) error {
	request := &ChatRequest{
		Model:       s.config.Model,
		Messages:    messages,
		Temperature: s.config.Temperature,
		TopP:        s.config.TopP,
		MaxTokens:   s.config.MaxContextTokens,
		Stream:      true,
	}

	return s.sendStreamRequest(ctx, request, callback)
}

// sendChatRequest 发送对话请求
func (s *DeepSeekService) sendChatRequest(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.APIURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.APIKey)

	// 重试机制
	var resp *http.Response
	var lastErr error

	for i := 0; i <= s.config.MaxRetries; i++ {
		resp, lastErr = s.httpClient.Do(req)
		if lastErr == nil && resp.StatusCode < 500 {
			break
		}

		if i < s.config.MaxRetries {
			backoff := time.Duration(i+1) * time.Second
			s.logger.WithFields(logrus.Fields{
				"attempt": i + 1,
				"backoff": backoff,
				"error":   lastErr,
			}).Warn("Request failed, retrying")

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoff):
			}
		}
	}

	if lastErr != nil {
		return nil, fmt.Errorf("request failed after %d retries: %w", s.config.MaxRetries, lastErr)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response ChatResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"model":        response.Model,
		"total_tokens": response.Usage.TotalTokens,
		"choices":      len(response.Choices),
	}).Debug("Chat request completed")

	return &response, nil
}

// sendStreamRequest 发送流式请求
func (s *DeepSeekService) sendStreamRequest(ctx context.Context, request *ChatRequest, callback func(string) error) error {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.APIURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.APIKey)
	req.Header.Set("Accept", "text/event-stream")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 处理SSE流
	reader := resp.Body
	buffer := make([]byte, 4096)
	var incomplete string

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		n, err := reader.Read(buffer)
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("failed to read stream: %w", err)
		}

		data := incomplete + string(buffer[:n])
		lines := strings.Split(data, "\n")
		incomplete = lines[len(lines)-1]
		lines = lines[:len(lines)-1]

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || !strings.HasPrefix(line, "data: ") {
				continue
			}

			data := strings.TrimPrefix(line, "data: ")
			if data == "[DONE]" {
				return nil
			}

			var streamResponse struct {
				Choices []struct {
					Delta struct {
						Content string `json:"content"`
					} `json:"delta"`
				} `json:"choices"`
			}

			if err := json.Unmarshal([]byte(data), &streamResponse); err != nil {
				s.logger.WithField("data", data).Warn("Failed to parse stream data")
				continue
			}

			if len(streamResponse.Choices) > 0 {
				content := streamResponse.Choices[0].Delta.Content
				if content != "" {
					if err := callback(content); err != nil {
						return fmt.Errorf("callback error: %w", err)
					}
				}
			}
		}
	}

	return nil
}

// IntentRecognizer 意图识别器
type IntentRecognizer struct {
	deepseek *DeepSeekService
	logger   *logrus.Logger
}

// NewIntentRecognizer 创建意图识别器
func NewIntentRecognizer(deepseek *DeepSeekService, logger *logrus.Logger) *IntentRecognizer {
	return &IntentRecognizer{
		deepseek: deepseek,
		logger:   logger,
	}
}

// Intent 意图
type Intent struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command,omitempty"`
}

// RecognizeIntent 识别意图
func (r *IntentRecognizer) RecognizeIntent(ctx context.Context, userInput string) (*Intent, error) {
	systemPrompt := `你是一个专业的运维意图识别助手。请精确分析用户输入，识别其意图类型和相关参数。

支持的意图类型：
1. host_management - 主机管理（查看、添加、删除、测试连接）
2. command_execution - 执行系统命令
3. system_monitoring - 系统监控（CPU、内存、磁盘、网络使用率）
4. service_management - 服务管理（启动、停止、重启服务）
5. log_analysis - 日志分析和查看
6. file_operations - 文件操作（查看、编辑、传输）
7. network_diagnostics - 网络诊断（ping、telnet、端口检查）
8. security_check - 安全检查（权限、进程、端口扫描）
9. backup_restore - 备份和恢复操作
10. alert_management - 告警管理
11. report_generation - 报表生成
12. general_chat - 一般对话

【重要】主机添加意图识别规则：
当用户输入包含以下模式时，必须识别为host_management类型的add_host操作：
1. "添加主机 [IP] [用户名] [密码]" - 标准格式
2. "[IP] [用户名] [密码]" - 纯参数格式（三个参数，第一个是IP地址）
3. "主机添加 [IP] [用户名] [密码]" - 变体格式
4. "新增主机 [IP] [用户名] [密码]" - 变体格式

IP地址识别：符合IPv4格式（如***********、********、**********等）
用户名识别：常见系统用户名（root、admin、ubuntu、user等）
密码识别：任意字符串，可包含特殊字符（如1qaz#EDC、P@ssw0rd等）

请返回JSON格式，包含：
- type: 意图类型
- confidence: 置信度(0-1)
- parameters: 相关参数

示例：
用户输入："查看所有主机状态"
返回：{"type":"host_management","confidence":0.95,"parameters":{"action":"list","filter":"status"}}

用户输入："查询现有主机账号"
返回：{"type":"host_management","confidence":0.95,"parameters":{"action":"list","filter":"account"}}

用户输入："查询主机账号信息"
返回：{"type":"host_management","confidence":0.95,"parameters":{"action":"list","filter":"account_info"}}

用户输入："显示主机账号信息"
返回：{"type":"host_management","confidence":0.9,"parameters":{"action":"list","filter":"account_info"}}

用户输入："现有主机"
返回：{"type":"host_management","confidence":0.85,"parameters":{"action":"list"}}

用户输入："主机账号"
返回：{"type":"host_management","confidence":0.85,"parameters":{"action":"list","filter":"account"}}

用户输入："查询主机信息"
返回：{"type":"host_management","confidence":0.9,"parameters":{"action":"list","filter":"info"}}

【主机添加专用示例】：
用户输入："添加主机 ************** root 1qaz#EDC"
返回：{"type":"host_management","confidence":0.98,"parameters":{"action":"add_host","ip":"**************","username":"root","password":"1qaz#EDC"}}

用户输入："************** root 1qaz#EDC"
返回：{"type":"host_management","confidence":0.95,"parameters":{"action":"add_host","ip":"**************","username":"root","password":"1qaz#EDC"}}

用户输入："主机添加 ********** admin P@ssw0rd"
返回：{"type":"host_management","confidence":0.95,"parameters":{"action":"add_host","ip":"**********","username":"admin","password":"P@ssw0rd"}}

用户输入："新增主机 *********** ubuntu mypassword123"
返回：{"type":"host_management","confidence":0.93,"parameters":{"action":"add_host","ip":"***********","username":"ubuntu","password":"mypassword123"}}

用户输入："添加主机 ************ admin mypass"
返回：{"type":"host_management","confidence":0.9,"parameters":{"action":"add_host","ip":"************","username":"admin","password":"mypass"}}

用户输入："主机添加"
返回：{"type":"host_management","confidence":0.8,"parameters":{"action":"add_host"}}

用户输入："检查web-01服务器的CPU使用率"
返回：{"type":"system_monitoring","confidence":0.9,"parameters":{"host":"web-01","metric":"cpu","action":"check"}}

用户输入："查看主机*************的内存使用情况"
返回：{"type":"system_monitoring","confidence":0.95,"parameters":{"host":"*************","metric":"memory","action":"view"}}

用户输入："监控所有主机的磁盘空间"
返回：{"type":"system_monitoring","confidence":0.9,"parameters":{"metric":"disk","action":"monitor","scope":"all"}}

用户输入："显示系统负载"
返回：{"type":"system_monitoring","confidence":0.85,"parameters":{"metric":"load","action":"show"}}

用户输入："在数据库服务器上执行ps aux命令"
返回：{"type":"command_execution","confidence":0.9,"parameters":{"command":"ps aux","host_type":"database"}}

用户输入："重启nginx服务"
返回：{"type":"service_management","confidence":0.95,"parameters":{"action":"restart","service":"nginx"}}

用户输入："启动mysql"
返回：{"type":"service_management","confidence":0.9,"parameters":{"action":"start","service":"mysql"}}

用户输入："停止redis服务"
返回：{"type":"service_management","confidence":0.9,"parameters":{"action":"stop","service":"redis"}}

用户输入："查看最近的错误日志"
返回：{"type":"log_analysis","confidence":0.9,"parameters":{"log_type":"error","time_range":"recent","action":"view"}}

用户输入："搜索包含failed的日志"
返回：{"type":"log_analysis","confidence":0.9,"parameters":{"keyword":"failed","action":"search"}}

用户输入："分析系统日志"
返回：{"type":"log_analysis","confidence":0.85,"parameters":{"log_type":"system","action":"analyze"}}

用户输入："查看当前告警"
返回：{"type":"alert_management","confidence":0.9,"parameters":{"action":"list","status":"active"}}

用户输入："确认告警123"
返回：{"type":"alert_management","confidence":0.95,"parameters":{"action":"acknowledge","alert_id":"123"}}`

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userInput},
	}

	response, err := r.deepseek.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to recognize intent: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	content := response.Choices[0].Message.Content

	// 尝试解析JSON
	var intent Intent
	if err := json.Unmarshal([]byte(content), &intent); err != nil {
		// 如果解析失败，返回通用对话意图
		return &Intent{
			Type:       "general_chat",
			Confidence: 0.5,
			Parameters: map[string]interface{}{"original_input": userInput},
		}, nil
	}

	r.logger.WithFields(logrus.Fields{
		"user_input": userInput,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Debug("Intent recognized")

	return &intent, nil
}

// CommandGenerator 命令生成器
type CommandGenerator struct {
	deepseek *DeepSeekService
	logger   *logrus.Logger
}

// NewCommandGenerator 创建命令生成器
func NewCommandGenerator(deepseek *DeepSeekService, logger *logrus.Logger) *CommandGenerator {
	return &CommandGenerator{
		deepseek: deepseek,
		logger:   logger,
	}
}

// GenerateCommand 生成命令
func (g *CommandGenerator) GenerateCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	// 根据意图类型选择不同的命令生成策略
	switch intent.Type {
	case "system_monitoring":
		return g.generateMonitoringCommand(ctx, intent, userInput)
	case "service_management":
		return g.generateServiceCommand(ctx, intent, userInput)
	case "log_analysis":
		return g.generateLogCommand(ctx, intent, userInput)
	case "file_operations":
		return g.generateFileCommand(ctx, intent, userInput)
	case "network_diagnostics":
		return g.generateNetworkCommand(ctx, intent, userInput)
	case "command_execution":
		return g.generateGenericCommand(ctx, intent, userInput)
	default:
		return g.generateGenericCommand(ctx, intent, userInput)
	}
}

// generateMonitoringCommand 生成监控命令
func (g *CommandGenerator) generateMonitoringCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux系统监控专家。根据用户需求生成安全的系统监控命令。

支持的监控类型：
- CPU使用率: top -bn1 | head -20
- 内存使用: free -h
- 磁盘使用: df -h
- 磁盘IO: iostat -x 1 1
- 网络流量: iftop -t -s 10 (如果可用) 或 cat /proc/net/dev
- 系统负载: uptime
- 进程监控: ps aux --sort=-%cpu | head -20
- 端口监听: netstat -tuln | grep LISTEN

请只返回命令，不要包含解释。确保命令安全且不会对系统造成影响。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateServiceCommand 生成服务管理命令
func (g *CommandGenerator) generateServiceCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux服务管理专家。根据用户需求生成安全的服务管理命令。

支持的服务操作：
- 查看服务状态: systemctl status <service>
- 启动服务: systemctl start <service>
- 停止服务: systemctl stop <service>
- 重启服务: systemctl restart <service>
- 重载配置: systemctl reload <service>
- 查看服务日志: journalctl -u <service> -n 50
- 列出所有服务: systemctl list-units --type=service

注意：只生成查看状态的命令，不要生成启动/停止/重启命令，这些需要用户确认。
请只返回命令，不要包含解释。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateLogCommand 生成日志查看命令
func (g *CommandGenerator) generateLogCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux日志分析专家。根据用户需求生成安全的日志查看命令。

常用日志命令：
- 系统日志: journalctl -n 50
- 内核日志: dmesg | tail -50
- 认证日志: tail -50 /var/log/auth.log
- 系统消息: tail -50 /var/log/syslog
- 错误日志: journalctl -p err -n 50
- 特定服务日志: journalctl -u <service> -n 50
- 实时日志: journalctl -f
- 按时间查看: journalctl --since "1 hour ago"

请只返回命令，不要包含解释。确保命令安全且只读取日志。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateFileCommand 生成文件操作命令
func (g *CommandGenerator) generateFileCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux文件系统专家。根据用户需求生成安全的文件查看命令。

安全的文件操作命令：
- 查看文件内容: cat <file> 或 less <file>
- 查看文件头部: head -20 <file>
- 查看文件尾部: tail -20 <file>
- 查看目录内容: ls -la <directory>
- 查找文件: find <path> -name "<pattern>" -type f
- 查看文件大小: du -sh <file/directory>
- 查看文件权限: stat <file>
- 查看磁盘使用: df -h

注意：只生成查看和读取命令，不要生成修改、删除或移动文件的命令。
请只返回命令，不要包含解释。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateNetworkCommand 生成网络诊断命令
func (g *CommandGenerator) generateNetworkCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个网络诊断专家。根据用户需求生成安全的网络诊断命令。

常用网络诊断命令：
- 测试连通性: ping -c 4 <host>
- 测试端口: telnet <host> <port> 或 nc -zv <host> <port>
- 查看路由: traceroute <host>
- 查看网络接口: ip addr show 或 ifconfig
- 查看网络连接: netstat -tuln
- 查看监听端口: ss -tuln
- 查看ARP表: arp -a
- 查看DNS解析: nslookup <domain> 或 dig <domain>

请只返回命令，不要包含解释。确保命令安全且不会对网络造成影响。`

	prompt := fmt.Sprintf("用户需求: %s\n参数: %v", userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// generateGenericCommand 生成通用命令
func (g *CommandGenerator) generateGenericCommand(ctx context.Context, intent *Intent, userInput string) (string, error) {
	systemPrompt := `你是一个Linux系统管理专家，根据用户的意图和输入生成相应的Linux命令。
请只返回命令本身，不要包含任何解释或额外文本。
确保命令是安全的，不会对系统造成损害。

安全命令示例：
- 查看CPU使用率: top -bn1 | head -20
- 查看内存使用: free -h
- 查看磁盘使用: df -h
- 查看进程: ps aux
- 查看网络连接: netstat -tuln
- 查看系统负载: uptime
- 查看日志: tail -50 /var/log/syslog

禁止生成的危险命令：
- 删除文件: rm, rmdir
- 格式化: mkfs, fdisk
- 系统控制: shutdown, reboot, halt
- 用户管理: userdel, passwd
- 权限修改: chmod 777, chown -R`

	prompt := fmt.Sprintf("意图类型: %s\n用户输入: %s\n参数: %v\n请生成对应的安全Linux命令:", intent.Type, userInput, intent.Parameters)
	return g.executePrompt(ctx, systemPrompt, prompt)
}

// executePrompt 执行提示并返回清理后的命令
func (g *CommandGenerator) executePrompt(ctx context.Context, systemPrompt, userPrompt string) (string, error) {
	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := g.deepseek.Chat(ctx, messages)
	if err != nil {
		return "", fmt.Errorf("failed to generate command: %w", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response from AI")
	}

	command := strings.TrimSpace(response.Choices[0].Message.Content)

	// 清理命令格式
	command = g.cleanCommand(command)

	// 安全检查
	if !g.isCommandSafe(command) {
		return "", fmt.Errorf("generated command is not safe: %s", command)
	}

	g.logger.WithFields(logrus.Fields{
		"command": command,
	}).Debug("Command generated and validated")

	return command, nil
}

// cleanCommand 清理命令格式
func (g *CommandGenerator) cleanCommand(command string) string {
	// 移除可能的代码块标记
	command = strings.TrimPrefix(command, "```bash")
	command = strings.TrimPrefix(command, "```sh")
	command = strings.TrimPrefix(command, "```")
	command = strings.TrimSuffix(command, "```")
	command = strings.TrimSpace(command)

	// 移除可能的解释文本
	lines := strings.Split(command, "\n")
	if len(lines) > 0 {
		command = strings.TrimSpace(lines[0])
	}

	return command
}

// isCommandSafe 检查命令安全性
func (g *CommandGenerator) isCommandSafe(command string) bool {
	// 危险命令列表
	dangerousCommands := []string{
		"rm ", "rmdir", "dd if=", "mkfs", "fdisk", "parted",
		"shutdown", "reboot", "halt", "poweroff", "init 0", "init 6",
		"passwd", "userdel", "groupdel", "useradd", "usermod",
		"iptables -F", "ufw --force", "ufw reset",
		"chmod 777", "chown -R", "chmod -R 777",
		"format", "del /f", "rmdir /s",
		"kill -9", "killall", "pkill",
		"mount", "umount", "fsck",
		"crontab -r", "crontab -e",
		"history -c", "history -w",
		"> /dev/", ">> /dev/",
	}

	commandLower := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		if strings.Contains(commandLower, dangerous) {
			return false
		}
	}

	// 检查是否包含管道到危险位置
	if strings.Contains(commandLower, "> /") || strings.Contains(commandLower, ">> /") {
		return false
	}

	// 检查是否包含sudo（需要特殊处理）
	if strings.HasPrefix(commandLower, "sudo ") {
		return false
	}

	return true
}
