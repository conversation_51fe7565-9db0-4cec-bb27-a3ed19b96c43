package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试主机账号查询功能修复
func main() {
	baseURL := "http://localhost:8766/api/v1"

	fmt.Println("=== 测试主机账号查询功能修复 ===")

	// 等待服务器启动
	fmt.Println("等待服务器启动...")
	time.Sleep(3 * time.Second)

	// 测试各种查询表达方式
	testQueries := []string{
		"查询现有主机账号",
		"查询主机账号信息",
		"显示主机账号信息",
		"现有主机",
		"主机账号",
		"查询主机信息",
		"查看主机",
		"主机列表",
	}

	for i, query := range testQueries {
		fmt.Printf("\n%d. 测试查询: \"%s\"\n", i+1, query)
		testAIQuery(baseURL, query)
		time.Sleep(2 * time.Second) // 避免请求过快
	}

	// 直接测试list_hosts工具
	fmt.Println("\n9. 直接测试list_hosts工具")
	testListHostsTool(baseURL)
}

func testAIQuery(baseURL, query string) {
	data := map[string]interface{}{
		"message":    query,
		"session_id": "test-session-" + fmt.Sprintf("%d", time.Now().Unix()),
		"user_id":    1,
	}

	response := makeRequest("POST", baseURL+"/ai/message", data)
	if response != nil {
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err == nil {
			if content, ok := result["content"].(string); ok {
				fmt.Printf("AI响应: %s\n", content[:min(200, len(content))])

				// 检查是否调用了工具
				if toolCalls, ok := result["tool_calls"]; ok {
					fmt.Printf("工具调用: %v\n", toolCalls)
				}

				// 检查是否包含主机信息
				if containsHostInfo(content) {
					fmt.Println("✅ 包含主机信息")
				} else {
					fmt.Println("❌ 未包含主机信息")
				}
			}
		} else {
			fmt.Printf("响应解析失败: %s\n", string(response))
		}
	}
}

func testListHostsTool(baseURL string) {
	response := makeRequest("GET", baseURL+"/hosts", nil)
	if response != nil {
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err == nil {
			if data, ok := result["data"].(map[string]interface{}); ok {
				if hosts, ok := data["hosts"].([]interface{}); ok {
					fmt.Printf("主机数量: %d\n", len(hosts))
					if len(hosts) > 0 {
						host := hosts[0].(map[string]interface{})
						fmt.Printf("示例主机信息: ID=%v, 名称=%v, IP=%v, 用户名=%v\n",
							host["id"], host["name"], host["ip_address"], host["username"])
						fmt.Println("✅ list_hosts工具返回完整主机账号信息")
					}
				}
			}
		}
	}
}

func containsHostInfo(content string) bool {
	keywords := []string{"主机", "IP", "用户名", "端口", "192.168", "root"}
	for _, keyword := range keywords {
		if contains(content, keyword) {
			return true
		}
	}
	return false
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) &&
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func makeRequest(method, url string, data interface{}) []byte {
	var body io.Reader

	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			fmt.Printf("JSON编码失败: %v\n", err)
			return nil
		}
		body = bytes.NewBuffer(jsonData)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return nil
	}

	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return nil
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return nil
	}

	fmt.Printf("状态码: %d\n", resp.StatusCode)
	return responseBody
}
