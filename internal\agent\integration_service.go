package agent

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"
	"github.com/sirupsen/logrus"
)

// AgentIntegrationService Agent平台集成服务
type AgentIntegrationService struct {
	platform *AgentPlatform
	logger   *logrus.Logger
	config   *IntegrationConfig

	// 兼容性支持
	fallbackService service.AIServiceInterface
	enabled         bool
}

// IntegrationConfig 集成配置
type IntegrationConfig struct {
	EnableFallback       bool          `json:"enable_fallback"`
	FallbackThreshold    float64       `json:"fallback_threshold"`
	MaxProcessingTime    time.Duration `json:"max_processing_time"`
	EnableAsyncExecution bool          `json:"enable_async_execution"`
	EnableDetailedLogs   bool          `json:"enable_detailed_logs"`
}

// NewAgentIntegrationService 创建Agent集成服务
func NewAgentIntegrationService(
	deepseekService *service.DeepSeekService,
	hostService workflow.HostServiceInterface,
	fallbackService service.AIServiceInterface,
	logger *logrus.Logger,
) *AgentIntegrationService {
	config := &IntegrationConfig{
		EnableFallback:       true,
		FallbackThreshold:    0.6,
		MaxProcessingTime:    5 * time.Minute,
		EnableAsyncExecution: true,
		EnableDetailedLogs:   true,
	}

	// 创建Agent平台
	platform := NewAgentPlatform(deepseekService, hostService, logger)

	return &AgentIntegrationService{
		platform:        platform,
		logger:          logger,
		config:          config,
		fallbackService: fallbackService,
		enabled:         true,
	}
}

// Initialize 初始化集成服务
func (ais *AgentIntegrationService) Initialize(ctx context.Context) error {
	ais.logger.Info("Initializing Agent Integration Service")

	if err := ais.platform.Start(ctx); err != nil {
		return fmt.Errorf("failed to start agent platform: %w", err)
	}

	ais.logger.Info("Agent Integration Service initialized successfully")
	return nil
}

// ProcessMessage 处理消息（实现AIServiceInterface）
func (ais *AgentIntegrationService) ProcessMessage(ctx context.Context, req *service.ProcessMessageRequest) (*service.ProcessMessageResponse, error) {
	start := time.Now()

	if !ais.enabled {
		if ais.fallbackService != nil {
			return ais.fallbackService.ProcessMessage(ctx, req)
		}
		return nil, fmt.Errorf("agent platform is disabled and no fallback service available")
	}

	ais.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("AgentIntegrationService: Processing message")

	// 转换为平台请求
	platformReq := &PlatformRequest{
		UserMessage: req.Message,
		SessionID:   req.SessionID,
		UserID:      req.UserID,
		TraceID:     generateTraceID(),
		Context:     make(map[string]interface{}),
		Options:     make(map[string]interface{}),
	}

	// 使用Agent平台处理
	platformResp, err := ais.platform.ProcessRequest(ctx, platformReq)
	if err != nil {
		ais.logger.WithError(err).Error("AgentIntegrationService: Platform processing failed")

		// 降级到原有服务
		if ais.config.EnableFallback && ais.fallbackService != nil {
			ais.logger.Info("AgentIntegrationService: Falling back to original AI service")
			return ais.fallbackService.ProcessMessage(ctx, req)
		}

		return nil, fmt.Errorf("agent platform processing failed: %w", err)
	}

	// 转换响应格式
	response := &service.ProcessMessageResponse{
		Content:        platformResp.Message,
		Intent:         ais.extractIntent(platformResp),
		Confidence:     ais.extractConfidence(platformResp),
		Parameters:     ais.extractParameters(platformResp),
		TokenCount:     ais.estimateTokenCount(platformResp),
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}

	ais.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"success":         platformResp.Success,
		"agents_selected": ais.getAgentsCount(platformResp),
		"processing_time": response.ProcessingTime,
	}).Info("AgentIntegrationService: Message processing completed")

	return response, nil
}

// ExtractIntent 提取意图（实现AIServiceInterface）
func (ais *AgentIntegrationService) ExtractIntent(ctx context.Context, message string, context *service.ConversationContext) (*service.IntentResult, error) {
	if !ais.enabled {
		if ais.fallbackService != nil {
			return ais.fallbackService.ExtractIntent(ctx, message, context)
		}
		return nil, fmt.Errorf("agent platform is disabled")
	}

	ais.logger.WithField("message", message).Info("AgentIntegrationService: Extracting intent")

	// 创建简化的平台请求用于意图提取
	platformReq := &PlatformRequest{
		UserMessage: message,
		SessionID:   context.SessionID,
		UserID:      context.UserID,
		TraceID:     generateTraceID(),
		Context:     context.Variables,
		Options:     map[string]interface{}{"intent_only": true},
	}

	// 使用决策引擎进行意图分析
	decisionReq := &DecisionRequest{
		UserMessage: message,
		Context: &DecisionContext{
			SessionID: context.SessionID,
			UserID:    context.UserID,
			Variables: context.Variables,
		},
	}

	decisionResult, err := ais.platform.decisionEngine.MakeDecision(ctx, decisionReq)
	if err != nil {
		// 降级处理
		if ais.config.EnableFallback && ais.fallbackService != nil {
			return ais.fallbackService.ExtractIntent(ctx, message, context)
		}
		return nil, fmt.Errorf("intent extraction failed: %w", err)
	}

	// 转换结果
	result := &service.IntentResult{
		Type:       ais.inferIntentType(decisionResult),
		Confidence: decisionResult.Confidence,
		Parameters: ais.extractParametersFromDecision(decisionResult),
		Command:    ais.generateCommandSummary(decisionResult),
	}

	ais.logger.WithFields(logrus.Fields{
		"intent":     result.Type,
		"confidence": result.Confidence,
	}).Info("AgentIntegrationService: Intent extraction completed")

	return result, nil
}

// ProcessMessageWithAgents 使用Agent平台处理消息（扩展接口）
func (ais *AgentIntegrationService) ProcessMessageWithAgents(ctx context.Context, req *PlatformRequest) (*PlatformResponse, error) {
	if !ais.enabled {
		return nil, fmt.Errorf("agent platform is disabled")
	}

	return ais.platform.ProcessRequest(ctx, req)
}

// GetAgentCapabilities 获取Agent能力信息
func (ais *AgentIntegrationService) GetAgentCapabilities() map[string]interface{} {
	if !ais.enabled {
		return map[string]interface{}{"enabled": false}
	}

	capabilities := ais.platform.GetAgentCapabilities()
	capabilities["enabled"] = true
	capabilities["platform_status"] = ais.platform.GetStatus()

	return capabilities
}

// GetExecutionSession 获取执行会话
func (ais *AgentIntegrationService) GetExecutionSession(sessionID string) (*ExecutionSession, error) {
	if !ais.enabled {
		return nil, fmt.Errorf("agent platform is disabled")
	}

	return ais.platform.GetExecutionSession(sessionID)
}

// CancelExecution 取消执行
func (ais *AgentIntegrationService) CancelExecution(sessionID string) error {
	if !ais.enabled {
		return fmt.Errorf("agent platform is disabled")
	}

	return ais.platform.CancelExecution(sessionID)
}

// IsEnabled 检查是否启用
func (ais *AgentIntegrationService) IsEnabled() bool {
	return ais.enabled
}

// Enable 启用Agent平台
func (ais *AgentIntegrationService) Enable() {
	ais.enabled = true
	ais.logger.Info("AgentIntegrationService: Enabled")
}

// Disable 禁用Agent平台
func (ais *AgentIntegrationService) Disable() {
	ais.enabled = false
	ais.logger.Info("AgentIntegrationService: Disabled")
}

// GetStatistics 获取统计信息
func (ais *AgentIntegrationService) GetStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled": ais.enabled,
		"config":  ais.config,
	}

	if ais.enabled {
		stats["platform"] = ais.platform.GetStatistics()
	}

	return stats
}

// HealthCheck 健康检查
func (ais *AgentIntegrationService) HealthCheck(ctx context.Context) error {
	if !ais.enabled {
		return fmt.Errorf("agent platform is disabled")
	}

	health := ais.platform.HealthCheck(ctx)
	if status, ok := health["platform_status"].(PlatformStatus); ok {
		if status != PlatformStatusRunning {
			return fmt.Errorf("platform status is not running: %s", status)
		}
	}

	return nil
}

// 辅助方法

func (ais *AgentIntegrationService) extractIntent(resp *PlatformResponse) string {
	if resp.DecisionResult != nil && len(resp.DecisionResult.Agents) > 0 {
		// 根据选择的Agent推断意图
		firstAgent := resp.DecisionResult.Agents[0]
		switch firstAgent.AgentID {
		case "host_management_agent":
			return "host_management"
		case "system_monitoring_agent":
			return "system_monitoring"
		default:
			return "agent_execution"
		}
	}
	return "general_chat"
}

func (ais *AgentIntegrationService) extractConfidence(resp *PlatformResponse) float64 {
	if resp.DecisionResult != nil {
		return resp.DecisionResult.Confidence
	}
	return 0.8
}

func (ais *AgentIntegrationService) extractParameters(resp *PlatformResponse) map[string]interface{} {
	params := make(map[string]interface{})

	if resp.DecisionResult != nil {
		for _, agent := range resp.DecisionResult.Agents {
			for key, value := range agent.Parameters {
				params[key] = value
			}
		}
	}

	return params
}

func (ais *AgentIntegrationService) estimateTokenCount(resp *PlatformResponse) int {
	// 简单的token估算
	return len(resp.Message) / 4
}

func (ais *AgentIntegrationService) getAgentsCount(resp *PlatformResponse) int {
	if resp.DecisionResult != nil {
		return len(resp.DecisionResult.Agents)
	}
	return 0
}

func (ais *AgentIntegrationService) inferIntentType(decision *DecisionResult) string {
	if len(decision.Agents) == 0 {
		return "unknown"
	}

	// 根据第一个Agent的类型推断意图
	firstAgent := decision.Agents[0]
	switch firstAgent.AgentID {
	case "host_management_agent":
		return "host_management"
	case "system_monitoring_agent":
		return "system_monitoring"
	default:
		return "agent_execution"
	}
}

func (ais *AgentIntegrationService) extractParametersFromDecision(decision *DecisionResult) map[string]interface{} {
	params := make(map[string]interface{})

	for _, agent := range decision.Agents {
		for key, value := range agent.Parameters {
			params[key] = value
		}
	}

	return params
}

func (ais *AgentIntegrationService) generateCommandSummary(decision *DecisionResult) string {
	if len(decision.Agents) == 0 {
		return ""
	}

	if len(decision.Agents) == 1 {
		agent := decision.Agents[0]
		return fmt.Sprintf("%s.%s", agent.AgentID, agent.Capability)
	}

	return fmt.Sprintf("multi_agent_execution_%d", len(decision.Agents))
}

func generateTraceID() string {
	return fmt.Sprintf("trace_%d", time.Now().UnixNano())
}

// 实现其他AIServiceInterface方法的存根
func (ais *AgentIntegrationService) GetAvailableTools(userID int64) ([]service.ToolDefinition, error) {
	if ais.fallbackService != nil {
		return ais.fallbackService.GetAvailableTools(userID)
	}
	return []service.ToolDefinition{}, nil
}

func (ais *AgentIntegrationService) ExecuteTool(ctx context.Context, toolCall *service.ToolCall, context *service.ConversationContext) (*service.ToolResult, error) {
	if ais.fallbackService != nil {
		return ais.fallbackService.ExecuteTool(ctx, toolCall, context)
	}
	return nil, fmt.Errorf("tool execution not implemented in agent integration service")
}

func (ais *AgentIntegrationService) GenerateResponse(ctx context.Context, req *service.GenerateResponseRequest) (*service.GenerateResponseResult, error) {
	if ais.fallbackService != nil {
		return ais.fallbackService.GenerateResponse(ctx, req)
	}
	return nil, fmt.Errorf("response generation not implemented in agent integration service")
}

func (ais *AgentIntegrationService) SummarizeConversation(ctx context.Context, sessionID string) (*service.ConversationSummary, error) {
	if ais.fallbackService != nil {
		return ais.fallbackService.SummarizeConversation(ctx, sessionID)
	}
	return nil, fmt.Errorf("conversation summarization not implemented in agent integration service")
}

func (ais *AgentIntegrationService) ValidateCommand(ctx context.Context, command string, context *service.ConversationContext) (*service.CommandValidation, error) {
	if ais.fallbackService != nil {
		return ais.fallbackService.ValidateCommand(ctx, command, context)
	}
	return nil, fmt.Errorf("command validation not implemented in agent integration service")
}
